package dji.sampleV5.aircraft;

import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.getColorExt;
import static dji.sampleV5.aircraft.mvvm.ext.CommExtKt.getDrawableExt;

import android.annotation.SuppressLint;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.usage.UsageStats;
import android.app.usage.UsageStatsManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.Html;
import android.text.Spanned;
import android.text.TextUtils;
import android.util.Log;
import android.util.Pair;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.view.GravityCompat;
import androidx.lifecycle.ViewModelProvider;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.amap.api.maps.MapsInitializer;
import com.amap.api.maps.offlinemap.OfflineMapActivity;
import com.google.android.material.navigation.NavigationView;
import com.hjq.permissions.OnPermissionCallback;
import com.hjq.permissions.Permission;
import com.hjq.permissions.XXPermissions;
import com.hjq.toast.Toaster;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import dji.sampleV5.aircraft.data.preference.SpUtil;
import dji.sampleV5.aircraft.databinding.HomeActivityBinding;
import dji.sampleV5.aircraft.event.Event;
import dji.sampleV5.aircraft.lbs.LocationService;
import dji.sampleV5.aircraft.lbs.MapServiceFactory;
import dji.sampleV5.aircraft.mqtt.MQttManager;
import dji.sampleV5.aircraft.mvvm.base.BaseVMActivity;
import dji.sampleV5.aircraft.mvvm.base.MvvmHelperKt;
import dji.sampleV5.aircraft.mvvm.event.LiveDataEvent;
import dji.sampleV5.aircraft.mvvm.ext.CommExtKt;
import dji.sampleV5.aircraft.mvvm.ext.DialogExtKt;
import dji.sampleV5.aircraft.mvvm.ext.StorageExtKt;
import dji.sampleV5.aircraft.mvvm.key.ValueKey;
import dji.sampleV5.aircraft.mvvm.net.LoadStatusEntity;
import dji.sampleV5.aircraft.mvvm.net.request.RegisterSiteRequest;
import dji.sampleV5.aircraft.mvvm.net.request.RegisterUavRequest;
import dji.sampleV5.aircraft.mvvm.net.request.SiteLocationRequest;
import dji.sampleV5.aircraft.mvvm.net.response.DeviceModelBean;
import dji.sampleV5.aircraft.mvvm.net.response.OverallPlatFormInfo;
import dji.sampleV5.aircraft.mvvm.ui.activity.defect.DefectActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.defect.DefectViewModel;
import dji.sampleV5.aircraft.mvvm.ui.activity.history.HistoryListActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.log.LogManagerActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.multisite.MultisiteActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.report.NewReportActivity;
import dji.sampleV5.aircraft.mvvm.ui.activity.site.RegisterStationActivity;
import dji.sampleV5.aircraft.mvvm.update.listener.OnButtonClickListener;
import dji.sampleV5.aircraft.mvvm.update.manager.DownloadManager;
import dji.sampleV5.aircraft.mvvm.util.AMapLocationUtil;
import dji.sampleV5.aircraft.mvvm.util.AppUtils;
import dji.sampleV5.aircraft.mvvm.util.NetUtil;
import dji.sampleV5.aircraft.mvvm.util.XLogUtil;
import dji.sampleV5.aircraft.net.bean.LocateInfo;
import dji.sampleV5.aircraft.net.bean.NetConfig;
import dji.sampleV5.aircraft.net.bean.UAVInfoSN;
import dji.sampleV5.aircraft.page.FindAircraftActivity;
import dji.sampleV5.aircraft.page.RecentMissionDetailActivity;
import dji.sampleV5.aircraft.page.SiteListActivity;
import dji.sampleV5.aircraft.page.fly.Task.TaskHistoryListActivity;
import dji.sampleV5.aircraft.page.login.LoginCache;
import dji.sampleV5.aircraft.page.login.LoginMixActivity;
import dji.sampleV5.aircraft.page.picture.PictureActivity;
import dji.sampleV5.aircraft.page.web.WebViewActivity;
import dji.sampleV5.aircraft.util.GCJ02_WGS84;
import dji.sampleV5.aircraft.util.ToastUtil;
import dji.sampleV5.aircraft.util.Util;
import dji.sdk.keyvalue.key.CameraKey;
import dji.sdk.keyvalue.key.FlightControllerKey;
import dji.sdk.keyvalue.key.GimbalKey;
import dji.sdk.keyvalue.key.KeyTools;
import dji.sdk.keyvalue.key.ProductKey;
import dji.sdk.keyvalue.utils.ProductUtil;
import dji.sdk.keyvalue.value.camera.CameraStorageInfo;
import dji.sdk.keyvalue.value.camera.CameraStorageInfos;
import dji.sdk.keyvalue.value.camera.CameraStorageLocation;
import dji.sdk.keyvalue.value.camera.CameraType;
import dji.sdk.keyvalue.value.camera.CameraVideoStreamSourceType;
import dji.sdk.keyvalue.value.camera.SDCardLoadState;
import dji.sdk.keyvalue.value.common.ComponentIndexType;
import dji.sdk.keyvalue.value.common.LocationCoordinate2D;
import dji.sdk.keyvalue.value.product.ProductType;
import dji.v5.common.callback.CommonCallbacks;
import dji.v5.common.error.IDJIError;
import dji.v5.common.register.DJISDKInitEvent;
import dji.v5.common.utils.GeoidManager;
import dji.v5.manager.KeyManager;
import dji.v5.manager.SDKManager;
import dji.v5.manager.account.LoginInfo;
import dji.v5.manager.account.UserAccountManager;
import dji.v5.manager.interfaces.SDKManagerCallback;
import dji.v5.utils.common.JsonUtil;
import dji.v5.utils.common.PermissionUtil;
import dji.v5.ux.core.communication.DefaultGlobalPreferences;
import dji.v5.ux.core.communication.GlobalPreferencesManager;
import dji.v5.ux.core.util.DataProcessor;
import dji.v5.ux.core.util.UxSharedPreferencesUtil;
import kotlin.Unit;
import me.jessyan.autosize.internal.CancelAdapt;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class HomeActivity extends BaseVMActivity<HomeViewModel, HomeActivityBinding> implements View.OnClickListener,
        NavigationView.OnNavigationItemSelectedListener, CancelAdapt {
    private static final String TAG = "HomeActivity";
    private final AtomicBoolean isInited = new AtomicBoolean(false);
    private AtomicBoolean isProductListenerRegistered = new AtomicBoolean(false);
    private static final int REQUEST_CODE = 0x0010;
    private boolean isConnect = false;

    private String uaName = "";
    private String currentSN = null; // 当前设备SN
    private boolean mqttConnectionInitialized = false; // MQ连接是否已初始化
    private long lastUpdateTime = 0; // 上次更新站点位置的时间
    private static final long UPDATE_INTERVAL = 5000; // 站点位置更新间隔（5秒）
    private DefectViewModel defectViewModel;
    private static final Map<ProductType, Pair<Integer, DeviceModelBean>> PRODUCT_INFO_MAP = new HashMap<>();
    static {
        PRODUCT_INFO_MAP.put(ProductType.M30_SERIES, new Pair<>(R.drawable.m30, new DeviceModelBean("经纬 M30T", "DJI_M30_T")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MAVIC_3_ENTERPRISE_SERIES, new Pair<>(R.drawable.mavic3, new DeviceModelBean("御 3 行业进阶版", "DJI_MAVICE3_T")));
        PRODUCT_INFO_MAP.put(ProductType.M300_RTK, new Pair<>(R.drawable.aircraft_m300, new DeviceModelBean("经纬 M300 RTK", "DJI_M300_RTK")));
        PRODUCT_INFO_MAP.put(ProductType.M350_RTK, new Pair<>(R.drawable.aircraft_m300, new DeviceModelBean("经纬 M350 RTK", "DJI_M350_RTK")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MINI_3_PRO, new Pair<>(R.drawable.minipro3, new DeviceModelBean("Mini 3 Pro ", "DJ mini pro 3")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MINI_3, new Pair<>(R.drawable.minipro3, new DeviceModelBean("Mini 3", "DJ mini pro 3")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_4_SERIES, new Pair<>(R.drawable.aircraft_matrice4t, new DeviceModelBean("Matrice 4T", "Matrice4T")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_4D_SERIES, new Pair<>(R.drawable.aircraft_matrice4d, new DeviceModelBean("Matrice 4TD", "Matrice4TD")));
        PRODUCT_INFO_MAP.put(ProductType.DJI_MATRICE_400, new Pair<>(R.drawable.ic_dji_matrice400, new DeviceModelBean("Matrice 400", "DJI_M400")));
    }
    private ArrayList<String> permissionList;
    {
        permissionList = new ArrayList<String>();
        permissionList.add(Permission.ACCESS_COARSE_LOCATION);
        permissionList.add(Permission.ACCESS_FINE_LOCATION);
        permissionList.add(Permission.READ_PHONE_STATE);
        permissionList.add(Permission.RECORD_AUDIO);
        permissionList.add(Permission.MANAGE_EXTERNAL_STORAGE);
//        permissionList.add(Permission.GET_INSTALLED_APPS);
    }
    private List<OverallPlatFormInfo> overallPlatFormInfos = new ArrayList<>();

    private DownloadManager manager;
    private LocationCoordinate2D uavLocation;
    private ActivityResultLauncher<String[]> requestPermissionLauncher;


    @Override
    public int getLayoutId() {
        return R.layout.home_activity;
    }

    @Override
    public void initView(@Nullable Bundle savedInstanceState) {
        View view = mBinding.navView.getHeaderView(0);
        ConstraintLayout constraintLayout = view.findViewById(R.id.combo_layout);
        constraintLayout.setOnClickListener(this);
        defectViewModel = new ViewModelProvider(this).get(DefectViewModel.class);
        registerDefUIChange(defectViewModel);
        mBinding.fly.setOnClickListener(this);
        mBinding.web.setOnClickListener(this);
        mBinding.media.setOnClickListener(this);
        mBinding.plan.setOnClickListener(this);
        mBinding.imgMenu.setOnClickListener(this);
        mBinding.btnFlightMode.setOnClickListener(this);
        mBinding.navView.setNavigationItemSelectedListener(this);
        mBinding.tvIsTest.setVisibility(NetConfig.isTest ? View.VISIBLE : View.GONE);

        boolean isConnectedFlight = StorageExtKt.getMmkv().getBoolean(ValueKey.IS_CONNECTED_FLIGHT, true);
        switchModeUI(isConnectedFlight);

        if (!TextUtils.equals(Util.getAppName(getApplicationContext()), SpUtil.getAPPName())) {
            startActivity(new Intent(this, LoginMixActivity.class));
            finish();
            return;
        }

        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache == null) {
            startActivity(new Intent(this, LoginMixActivity.class));
            finish();
            return;
        } else {
            long lastTime = loginCache.getTime();
            Log.e("TAG", "onResume: " + (System.currentTimeMillis() - lastTime) / 1000 / 60 / 60 / 24);
            if ((System.currentTimeMillis() - lastTime) / 1000 / 60 / 60 / 24 > 7) {
                startActivity(new Intent(this, LoginMixActivity.class));
                finish();
            } else {
                checkPermissionAndRequest();
                Event.post(loginCache); // 登录参数变化通知远程调用配置
            }

            View headView = mBinding.navView.getHeaderView(0);
            AlphabetCircleImageView ivAvatar = headView.findViewById(R.id.iv_nav_avatar);
            ivAvatar.setAlphabet(loginCache.getId());
            TextView tvUserName = headView.findViewById(R.id.tv_nav_name);
            tvUserName.setText(loginCache.getId());
            TextView tvOrgName = headView.findViewById(R.id.tv_org_name);
            tvOrgName.setVisibility(View.INVISIBLE);
        }

        UxSharedPreferencesUtil.initialize(this);
        GlobalPreferencesManager.initialize(new DefaultGlobalPreferences(this));
        GeoidManager.getInstance().init(this);
        //获取设备上是否存在其他平台APP
        getOtherPlatformApp();

        // 检查登录状态，决定是否需要连接MQ
        checkAndInitMQConnection();
    }

    @SuppressLint("NewApi")
    private void getOtherPlatformApp() {
        //检查设备是否安装巡电APP
        boolean isInstallXd = AppUtils.Companion.isAppInstalled(ContextUtil.getApplicationContext(), "dji.sampleV5.aircraft.xundian");
        //检查设备是否安装御风APP
        boolean isInstallYf = AppUtils.Companion.isAppInstalled(ContextUtil.getApplicationContext(), "com.dji.msdk5");
        //检查是否安装万象APP
        boolean isInstallWx = AppUtils.Companion.isAppInstalled(ContextUtil.getApplicationContext(), "dji.sampleV5.aircraft.wanxiang");
        List<Pair<String, Boolean>> installedSocialApps = new ArrayList<>();
        installedSocialApps.add(new Pair<>("巡电", isInstallXd));
        installedSocialApps.add(new Pair<>("御风", isInstallYf));
        installedSocialApps.add(new Pair<>("万象", isInstallWx));

        overallPlatFormInfos = installedSocialApps.stream()
                .filter(pair -> pair.second)
                .map(pair -> new OverallPlatFormInfo(
                        getIconResourceId(pair.first),
                        pair.first,
                        getPackageNameByAppName(pair.first)
                ))
                .collect(Collectors.toList());
    }

    private static int getIconResourceId(String appName) {
        switch (appName) {
            case "巡电":
                return R.mipmap.skysys_xd_app_icon;
            case "御风":
                return R.mipmap.skysys_yf_app_icon;
            case "万象":
                return R.mipmap.skysys_wx_app_icon;
            default:
                return 0; // 默认资源 ID
        }
    }

    private static String getPackageNameByAppName(String appName) {
        switch (appName) {
            case "巡电":
                return "dji.sampleV5.aircraft.xundian";
            case "御风":
                return "com.dji.msdk5";
            case "万象":
                return "dji.sampleV5.aircraft.wanxiang";
            default:
                return ""; // 默认包名
        }
    }

    @Override
    public void initData() {
        LiveDataEvent.INSTANCE.isUavRegister().observe(this, aBoolean -> {
            if (!aBoolean) {
                DialogExtKt.showDialogMessage(this, "该无人机尚未注册站点，是否立即注册？", "提示", "确定", () -> {
                            // 启动新的Compose注册流程
                            startStationRegistration();
                            return Unit.INSTANCE;
                        }, "取消", ()-> Unit.INSTANCE, true
                );
            }
        });
        LiveDataEvent.INSTANCE.isSdkInit().observeForever(isInit ->{
            productListener();
        });
        // 立即检查一次SDK状态
        if (SDKManager.getInstance().isRegistered()) {
            productListener();
        }
    }

    /**
     * 启动新的Compose站点注册流程
     */
    private void startStationRegistration() {
        Intent intent = new Intent(this, RegisterStationActivity.class);
        startActivity(intent);
    }

    private void productListener() {
        if (isProductListenerRegistered.getAndSet(true)) {
            return;
        }
        KeyManager.getInstance().listen(KeyTools.createKey(FlightControllerKey.KeyConnection), this, (oldValue, newValue) -> {
            if (newValue == null) {
                return;
            }
            isConnect = newValue;
            DJIAircraftApplication.getInstance().setAircraftConnected(newValue);
            if (newValue) {
                loginDJIAccount();
                getAircraftInfo();
                getSerialNumber();
                getCameraInfo();
                getCameraType();
                //监听SD卡状态
                setSDCardStateListener();
                updateSiteLocation();
            } else {
                ToastUtil.show("无人机断开连接");
                mBinding.tvName.setText("未连接");
                mBinding.tvSn.setText("无人机序列号");
                mBinding.imgAircraft.setImageResource(R.drawable.unconnect_aircraft);
                Drawable img = getResources().getDrawable(R.drawable.red_icon);
                img.setBounds(0, 0, img.getMinimumWidth(), img.getMinimumHeight());
                mBinding.tvName.setCompoundDrawables(img, null, null, null);
                mBinding.tvStatus.setText("请检查连接线");
            }
        });

    }

    private void loginDJIAccount() {
        //判断是否登录了大疆账号
        LoginInfo loginInfo = UserAccountManager.getInstance().getLoginInfo();
        if (loginInfo.getAccount() == null) {
            try {
                UserAccountManager.getInstance().logInDJIUserAccount(HomeActivity.this, false, new CommonCallbacks.CompletionCallback() {
                    @Override
                    public void onSuccess() {
                        ToastUtil.show("登录成功！");
                    }

                    @Override
                    public void onFailure(@NonNull IDJIError error) {
                        ToastUtil.show("登录失败:" + error.description());
                    }
                });
                ToastUtil.show("请登录大疆账号");
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            Log.e(TAG, ":login " + loginInfo.getAccount());
        }
    }

    //获取飞行器相关数据
    private void getAircraftInfo() {
        KeyManager.getInstance().getValue(KeyTools.createKey(ProductKey.KeyProductType), new CommonCallbacks.CompletionCallbackWithParam<ProductType>() {
            @Override
            public void onSuccess(ProductType productType) {
                DJIAircraftApplication.getInstance().setProductType(productType);
                Drawable img = getResources().getDrawable(R.drawable.green_icon);
                img.setBounds(0, 0, img.getMinimumWidth(), img.getMinimumHeight());
                mBinding.tvName.setCompoundDrawables(img, null, null, null);
                mBinding.tvStatus.setText("已连接");
                setProductInfo(productType);
            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                Log.e("register", "KeyProductType onFailure: " + error.description());
            }
        });
    }

    //获取设备码
    private void getSerialNumber() {
        KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeySerialNumber), new CommonCallbacks.CompletionCallbackWithParam<String>() {
            @Override
            public void onSuccess(String sn) {
                mBinding.tvSn.setText(sn);
                StorageExtKt.getMmkv().putString(ValueKey.UAV_SN, sn);
                currentSN = sn; // 记录当前SN
                XLogUtil.INSTANCE.e("KeySerialNumber", "onSuccess KeySerialNumber:" + sn);

                // 只有在用户已登录且在首页时才连接MQ
                if (mqttConnectionInitialized) {
                    XLogUtil.INSTANCE.d(TAG, "用户已登录，开始连接MQ，SN: " + sn);
                    MQttManager.getInstance().onUserLogin(sn);
                } else {
                    XLogUtil.INSTANCE.d(TAG, "MQ连接未初始化，跳过连接，SN: " + sn);
                }

                new Handler().postDelayed(() -> {
                    //更新站点信息
                    updateSiteLocation();
                }, 2000);

            }

            @Override
            public void onFailure(@NonNull IDJIError error) {
                XLogUtil.INSTANCE.d(TAG, "onFailure: " + error.description());
            }
        });
    }

    //监听设备SD卡状态
    private void setSDCardStateListener() {
        ComponentIndexType componentIndexType;
        if (ProductUtil.isM400Product()) {
            componentIndexType = ComponentIndexType.PORT_1;
        } else {
            componentIndexType = ComponentIndexType.LEFT_OR_MAIN;
        }
        KeyManager.getInstance().listen(KeyTools.createKey(CameraKey.KeyCameraStorageInfos, componentIndexType), this, (oldValue1, cameraStorageInfos) -> {
            if (cameraStorageInfos != null) {
                CameraStorageInfo sdcardInfo = cameraStorageInfos.getCameraStorageInfoByLocation(CameraStorageLocation.SDCARD);
                if (sdcardInfo != null) {
                    if (sdcardInfo.getStorageState() == SDCardLoadState.INSERTED) {
                        XLogUtil.INSTANCE.i("HomeActivity", "HomeActivity" + "  onSuccess: SD卡插入");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_SD_CARD, true);
                    } else {
                        XLogUtil.INSTANCE.i("HomeActivity", "HomeActivity" + "  onSuccess: SD卡拔出");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_SD_CARD, false);
                    }
                }
            }
        });
    }

    private void getCameraInfo(){
        //获取当前相机类型
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraType), new CommonCallbacks.CompletionCallbackWithParam<CameraType>() {
            @Override
            public void onSuccess(CameraType cameraType) {
                if (cameraType != null) {
                    StorageExtKt.getMmkv().putString(ValueKey.CAMERA_MODE, JsonUtil.toJson(cameraType));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e("HomeActivity", "onFailure: " + idjiError.description());
            }
        });
        //获取当前相机镜头类型
        KeyManager.getInstance().getValue(KeyTools.createKey(CameraKey.KeyCameraVideoStreamSource), new CommonCallbacks.CompletionCallbackWithParam<CameraVideoStreamSourceType>() {
            @Override
            public void onSuccess(CameraVideoStreamSourceType cameraVideoStreamSourceType) {
                if (cameraVideoStreamSourceType != null) {
                    StorageExtKt.getMmkv().putString(ValueKey.CAMERA_MODE, JsonUtil.toJson(cameraVideoStreamSourceType));
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                XLogUtil.INSTANCE.e("HomeActivity", "onFailure: " + idjiError.description());
            }
        });
    }

    private void getCameraType() {
        //判断当前连接的是哪个相机
        KeyManager.getInstance().getValue(KeyTools.createKey(GimbalKey.KeyConnection, ComponentIndexType.UP), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
            @Override
            public void onSuccess(Boolean upConnect) {
                Log.e("TAG", "onSuccess upConnect: "+ upConnect);
                if(upConnect){
                    SpUtil.setRCameraIndex(2);
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                Log.e("TAG", "onFailure upConnect: "+ idjiError.description());
            }
        });
        KeyManager.getInstance().getValue(KeyTools.createKey(GimbalKey.KeyConnection, ComponentIndexType.LEFT_OR_MAIN), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
            @Override
            public void onSuccess(Boolean leftConnect) {
                Log.e("TAG", "onSuccess leftConnect: "+ leftConnect);
                if(leftConnect){
                    SpUtil.setRCameraIndex(0);
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                Log.e("TAG", "onFailure leftConnect: "+ idjiError.description());
            }
        });
        KeyManager.getInstance().getValue(KeyTools.createKey(GimbalKey.KeyConnection, ComponentIndexType.RIGHT), new CommonCallbacks.CompletionCallbackWithParam<Boolean>() {
            @Override
            public void onSuccess(Boolean rightConnect) {
                Log.e("TAG", "onSuccess rightConnect: "+ rightConnect);
                if(rightConnect){
                    SpUtil.setRCameraIndex(1);
                }
            }

            @Override
            public void onFailure(@NonNull IDJIError idjiError) {
                Log.e("TAG", "onFailure rightConnect: "+ idjiError.description());

            }
        });
    }

    @Override
    public void onRequestSuccess() {
        mViewModel.getAppVersionLiveData().observe(this, this::startUpdate);
        mViewModel.getUpdateSiteLiveData().observe(this, site -> {
            if (site.equals("success")) {
                Toaster.show("站点位置已更新");
            }
        });
        defectViewModel.getSiteInfoLiveData().observe(this, siteInfo -> {
            StorageExtKt.getMmkv().putString(ValueKey.LOCATION_LIST_INFO, JsonUtil.toJson(siteInfo));
        });
    }

    @Override
    public void onRequestError(@NonNull LoadStatusEntity loadStatus) {
        String errorMsg = "请求码：" + loadStatus.getRequestCode()+ "错误码：" + loadStatus.getErrorCode() + "错误信息：" + loadStatus.getErrorMessage();
        DialogExtKt.showDialogMessage(this, errorMsg, "提示", "确定", () -> Unit.INSTANCE, "取消", ()-> Unit.INSTANCE, false);
    }

    private void checkPermissionAndRequest() {
        XXPermissions.with(this)
                .permission(permissionList)
                .request((permissions, allGranted) -> {
                    if (allGranted) {
                        MapsInitializer.updatePrivacyShow(HomeActivity.this, true, true);
                        MapsInitializer.updatePrivacyAgree(HomeActivity.this, true);
                        LocationService locationService = MapServiceFactory.getLocationServiceInstance();
                        locationService.connectService();
                        locationService.startOnceLocation();
                        AMapLocationUtil.INSTANCE.getLocation(this, (lat, lon) -> {
                            LocateInfo locateInfo = GCJ02_WGS84.gcj02_To_Wgs84(lat, lon);
                            List<String> locationInfo = new ArrayList<>();
                            locationInfo.add(String.format("%.7f", locateInfo.getLongitude()));
                            locationInfo.add(String.format("%.7f", locateInfo.getLatitude()));
                        });
                        //延迟2秒后执行请求
                        new Handler().postDelayed(() -> {
                            if (NetUtil.isConnected(MvvmHelperKt.getAppContext())) {
                                defectViewModel.getLocationData();
                                if (!NetConfig.isInnerNet) {
                                    mViewModel.appVersionCheck();
                                }
                            }
                        }, 2000);

                    }else {
                        showPermissionDialog();
                    }
                });
    }

    private void showPermissionDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this, R.style.AppTheme_Dialog);
        builder.setTitle("权限申请");
        builder.setMessage("如果没有允许权限应用可能无法正常工作，请到应用设置的权限管理中修改权限。");
        builder.setNegativeButton("取消", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
        });
        builder.setPositiveButton("确定", (dialog, which) -> {
            dialog.dismiss();
            ContextUtil.removeAllActivity();
            start2SettingPage();
        });
        builder.setCancelable(false);
        builder.show();
    }

    private void start2SettingPage() {
        Uri packageURI = Uri.parse("package:" + getPackageName());
        Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS, packageURI);
        startActivity(intent);
    }


    @Override
    protected void onResume() {
        super.onResume();
        boolean isConnectedFlight = StorageExtKt.getMmkv().getBoolean(ValueKey.IS_CONNECTED_FLIGHT, true);
        switchModeUI(isConnectedFlight);

        // 确保MQ连接状态与登录状态同步
        syncMQConnectionWithLoginState();
    }

    @Override
    public void onBackPressed() {
        //RTMPManager.getInstance().startPublish("rtmp://stream3.skysys.cn/xl_uav/ch666698013");
        super.onBackPressed();
        //MQttManager.getInstance().connectMQtt("1ZNBJ8D00C001F");
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Event.unregister(this);
        if (manager != null) {
            manager.cancel();
        }
        //flyStatusController.onDestroy();
        //MQttManager.getInstance().notifyAppClosed();
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_flight_mode:
                boolean isConnectedFlight = StorageExtKt.getMmkv().getBoolean(ValueKey.IS_CONNECTED_FLIGHT, true);
                boolean isNeverShow = StorageExtKt.getMmkv().getBoolean(ValueKey.IS_NEVER_SHOW_MODE_DIALOG, false);
                if (isNeverShow) {
                    if (isConnectedFlight) {
                        mBinding.btnFlightMode.setText("离线模式");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_CONNECTED_FLIGHT, false);
                        switchModeUI(false);
                    } else {
                        mBinding.btnFlightMode.setText("联网模式");
                        StorageExtKt.getMmkv().putBoolean(ValueKey.IS_CONNECTED_FLIGHT, true);
                        switchModeUI(true);
                    }
                } else {
                    DialogExtKt.showChangeTaskModeDialog(this,
                            mBinding.btnFlightMode.getText().toString(),
                            (isShow) ->{
                                StorageExtKt.getMmkv().putBoolean(ValueKey.IS_NEVER_SHOW_MODE_DIALOG, isShow);
                                if (isConnectedFlight) {
                                    mBinding.btnFlightMode.setText("离线模式");
                                    StorageExtKt.getMmkv().putBoolean(ValueKey.IS_CONNECTED_FLIGHT, false);
                                    switchModeUI(false);
                                } else {
                                    mBinding.btnFlightMode.setText("联网模式");
                                    StorageExtKt.getMmkv().putBoolean(ValueKey.IS_CONNECTED_FLIGHT, true);
                                    switchModeUI(true);
                                }
                                return Unit.INSTANCE;
                            },
                            (isShow) -> {
                                StorageExtKt.getMmkv().putBoolean(ValueKey.IS_CONNECTED_FLIGHT, isShow);
                                return Unit.INSTANCE;
                            }
                    );
                }
            case R.id.combo_layout:
               /* LoginCache loginCache = SpUtil.getLoginCache();
                if(loginCache == null){
                    startActivity(new Intent(this, LoginActivity.class));
                }*/
                break;

            // 抽屉菜单打开
            case R.id.img_menu:
                updateLoginInfo();//每次打开menu都更新登录信息
                mBinding.drawerLayout.openDrawer(GravityCompat.START);
                break;

            case R.id.fly:
                startActivity(new Intent(this, DefaultLayoutActivity.class));
                //startActivity(new Intent(this, TestActivity.class));
                break;
            case R.id.web:
                startActivity(new Intent(this, SiteListActivity.class));
//                freeInit();
                break;
            case R.id.plan:
                startActivity(new Intent(this, HistoryListActivity.class));
//                startActivity(new Intent(this, TaskHistoryListActivity.class));
                /*ToastUtil.show("开发中");*/
               /* ImmerseUtil.showDialog(new AddPlanDialog(this, new AddPlanDialog.MyClickListener() {
                    @Override
                    public void onCreatePolygon() {
                        Intent intent = new Intent(HomeActivity.this, WebViewActivity.class);
                        intent.putExtra("FromLogin", false);
                        intent.putExtra("type", 1);
                        startActivity(intent);
                    }

                    @Override
                    public void onCreatePoint() {
                        Intent intent = new Intent(HomeActivity.this, WebViewActivity.class);
                        intent.putExtra("FromLogin", false);
                        intent.putExtra("type", 2);
                        startActivity(intent);
                    }

                    @Override
                    public void onCreateOblique() {
                    }

                    @Override
                    public void onCreateCircle() {
                    }
                }));*/

                break;
            case R.id.media:
                if (!isConnect) {
                    ToastUtil.show("请先连接上飞机");
                    return;
                }
                startActivity(new Intent(this, PictureActivity.class));
                break;
        }
    }

    private void updateSiteLocation() {
        // 检查是否满足更新条件
        if (!shouldUpdateSiteLocation()) {
            return;
        }

        // 检查更新频率限制
        if (!canUpdateNow()) {
            XLogUtil.INSTANCE.d(TAG, "站点位置更新过于频繁，跳过本次更新");
            return;
        }

        uavLocation = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
        UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();

        // 再次验证数据完整性（防御性编程）
        if (uavInfoSN != null && uavInfoSN.getData() != null &&
            uavInfoSN.getData().getSiteInfo() != null &&
            uavInfoSN.getData().getSiteInfo().getSiteLocation() != null &&
            uavInfoSN.getData().getSiteInfo().getSiteID() != null) {

            double[] siteLocation = uavInfoSN.getData().getSiteInfo().getSiteLocation();
            String siteId = uavInfoSN.getData().getSiteInfo().getSiteID();

            if (uavLocation != null) {
                double[] location = new double[]{uavLocation.getLongitude(), uavLocation.getLatitude()};
                // 对比经纬度
                boolean isSameLocation = (siteLocation[0] == location[0]) && (siteLocation[1] == location[1]);

                XLogUtil.INSTANCE.d(TAG, "站点位置对比 - 当前位置: [" + location[0] + ", " + location[1] +
                                   "], 站点位置: [" + siteLocation[0] + ", " + siteLocation[1] + "], 相同: " + isSameLocation);

                if (!isSameLocation) {
                    SiteLocationRequest siteLocationRequest = new SiteLocationRequest();
                    siteLocationRequest.setSiteId(siteId);
                    siteLocationRequest.setSiteLocation(List.of(location[0], location[1]));
                    siteLocationRequest.setSiteEllipsAltitude(0);
                    mViewModel.updateSiteLocation(siteLocationRequest);
                    XLogUtil.INSTANCE.d(TAG, "执行站点位置更新 - 站点ID: " + siteId);
                } else {
                    XLogUtil.INSTANCE.d(TAG, "站点位置无变化，跳过更新");
                }
            } else {
                XLogUtil.INSTANCE.d(TAG, "无人机位置信息无效，跳过站点位置更新");
            }
        } else {
            XLogUtil.INSTANCE.e(TAG, "站点信息验证失败，这不应该发生（shouldUpdateSiteLocation已检查）");
        }
    }

    private void updateLoginInfo() {
        View view = mBinding.navView.getHeaderView(0);
        AlphabetCircleImageView ivAvatar = view.findViewById(R.id.iv_nav_avatar);
        TextView tvUserName = view.findViewById(R.id.tv_nav_name);
        TextView tvOrgName = view.findViewById(R.id.tv_org_name);
        ImageView exit = view.findViewById(R.id.exit);
        ivAvatar.setCircleBackgroundColor(Color.parseColor("#e3e3e3"));

        exit.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                XLogUtil.INSTANCE.d(TAG, "用户点击退出登录");

                // 先断开MQ连接
                MQttManager.getInstance().onUserLogout();

                // 清除本地状态
                mqttConnectionInitialized = false;
                currentSN = null;

                // 清除缓存,重新登录
                SpUtil.setLoginCache(null);
                startActivity(new Intent(HomeActivity.this, LoginMixActivity.class));
                finish();
            }
        });

    }

    private Spanned proName(String raw) {
        int index = raw.lastIndexOf(" ");
        if (index != -1) {
            return Html.fromHtml(raw.substring(0, index) + " <font color=\"red\">" + raw.substring(index, raw.length()) + "</font>");
        }
        return Html.fromHtml(raw);
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        switch (item.getItemId()) {
            case R.id.overall_platform:
                showPlatformPopupMenu();
                break;
            case R.id.sites_list:
                // 调度中心
                startActivity(new Intent(this, SiteListActivity.class));
                break;
            case R.id.offline_map:
                // 离线地图
                startActivity(new Intent(this, OfflineMapActivity.class));
                break;
            case R.id.recent_mission:
                if (TextUtils.isEmpty(SpUtil.getMissionBatch())) {
                    ToastUtil.show("没有待上传的任务");
                } else {
                    if (!isConnect) {
                        ToastUtil.show("请先连接上飞机");

                    } else {
                        startActivity(new Intent(this, RecentMissionDetailActivity.class));
                    }
                }
                break;
            case R.id.task_history:
                startActivity(new Intent(this, TaskHistoryListActivity.class));
                break;
            case R.id.ai_report:
                // 打开 ai 报告
                startActivity(new Intent(this, NewReportActivity.class));
                break;
            case R.id.find_aircraft:
                startActivity(new Intent(this, FindAircraftActivity.class));
                break;
            case R.id.defect_Info:
                startActivity(new Intent(this, DefectActivity.class));
                break;
            case R.id.multi_site:
                startActivity(new Intent(this, MultisiteActivity.class));
                break;
            case R.id.log_manager:
                startActivity(new Intent(this, LogManagerActivity.class));
                break;
            case R.id.site_register:
                startStationRegistration();
                break;
        }

        mBinding.drawerLayout.closeDrawer(GravityCompat.START);
        return true;
    }


    private void showPlatformPopupMenu() {
        if (overallPlatFormInfos.isEmpty()) {
            ToastUtil.show("暂无其他平台应用");
            return;
        }
        DialogExtKt.showOtherPlatformApp(this, overallPlatFormInfos, (platFormInfo) ->{
            jumpToNewAppAndFinish(platFormInfo.getPlatPackageName());
            return Unit.INSTANCE;
        });
    }

    private void jumpToNewAppAndFinish(String packageName) {
        try {
            //获取包管理器
            PackageManager packageManager = getPackageManager();
            //获取目标应用启动意图
            Intent intent = packageManager.getLaunchIntentForPackage(packageName);

            if (intent != null) {
                //设置新任务标志，确保在新的任务栈中启动
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                //启动目标应用
                startActivity(intent);
                //结束当前Activity
                finish();
                //结束当前应用进程
                android.os.Process.killProcess(android.os.Process.myPid());
                System.exit(0);
            } else {
                Toaster.show("找不到指定的应用");
            }
        } catch (Exception e) {
            XLogUtil.INSTANCE.e("HomeActivity", "跳转应用失败------" + e.getMessage());
        }
    }

    private void setProductInfo(ProductType productType) {
        if (PRODUCT_INFO_MAP.containsKey(productType)) {
            Pair<Integer, DeviceModelBean> productPair = PRODUCT_INFO_MAP.get(productType);
            uaName = productPair.second.getModelName();
            mBinding.imgAircraft.setImageResource(productPair.first);
            mBinding.tvName.setText(productPair.second.getModelName());
        }
    }

    private void startUpdate(LoginCache.AppVersionInfo apkVersion) {
        if (apkVersion.code == null) return;
        int size = apkVersion.size;
        double apkSize = (size > 0) ? CommExtKt.bytesToMegabytes(apkVersion.size) : 0.0;
        manager  = new DownloadManager.Builder(this)
                .apkUrl(apkVersion.url)
                .apkName(apkVersion.url.substring(apkVersion.url.lastIndexOf("/") + 1))
                .smallIcon(R.mipmap.ic_main)
                .apkVersionCode(Integer.parseInt(apkVersion.code))
                .apkVersionName(apkVersion.version)
                .apkSize(apkSize + "MB")
                .apkDescription(apkVersion.des)
                .enableLog(true)
                .jumpInstallPage(true)
                .dialogProgressBarColor(Color.parseColor("#9919a7f0"))
                .dialogButtonTextColor(Color.WHITE)
                .showNotification(true)
                .showBgdToast(false)
                .forcedUpgrade(false)
                .onButtonClickListener(id -> {
                    if (id == OnButtonClickListener.UPDATE) {
                        Toaster.show("开始下载，请稍后。。。");
                    }
                })
                .build();
        manager.download();
    }


    private void freeInit() {
        LoginCache loginCache = SpUtil.getLoginCache();
        String str_password = loginCache.getPwd();
        String userName = loginCache.getUserName();
        JSONObject data = new JSONObject();
        //data.put("secretKey", MD5.stringToMD5(userName + MD5.stringToMD5(str_password)));
        //data.put("secretKey", MD5.stringToMD5(userName + loginCache.getApiToken()));
        data.put("apiToken", loginCache.getApiToken());
        //data.put("userName", userName);
        data.put("timeout", 3000);

        OkHttpClient httpClient = new OkHttpClient();
        MediaType JSON = MediaType.parse("application/json;charset=utf-8");
        RequestBody requestBody = RequestBody.create(JSON, data.toJSONString());
        //String url = NetConfig.NJLOGIN + "transit/user/api/v1/passwordFreeInit";
        String url = NetConfig.FREEINIT + "login/passwordFreeInit";
        Request request = new Request.Builder().url(url).post(requestBody).build();

        Call call = httpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("TAG", "freeInit onFailure: " + call.toString() + "    error:" + e.getLocalizedMessage());
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                //在这里根据返回内容执行具体的操作
                //Log.e("TAG", "login onResponse: " + response.body().string());
                String result = response.body().string();
                Log.e("TAG", "freeInit onResponse: " + result);
                try {
                    JSONObject jsonObject = JSONObject.parseObject(result);
                    if (TextUtils.equals(jsonObject.getString("message"), "success")) {
                        startActivity(new Intent(HomeActivity.this, WebViewActivity.class));
                    } else {
                        ToastUtil.show(jsonObject.getString("message"));
                        //ToastUtil.show("无法查看场站缺陷，请联系管理员");
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void switchModeUI(boolean isConnect) {
        if (isConnect) {
            mBinding.btnFlightMode.setText("联网模式");
            mBinding.btnFlightMode.setTextColor(getColorExt(R.color.mission_mode_btn_connected_border));
            mBinding.btnFlightMode.setBackgroundDrawable(getDrawableExt(R.drawable.switch_flight_mode_connected_bg));
            mBinding.btnFlightMode.setCompoundDrawablesWithIntrinsicBounds(null, null, getDrawableExt(R.drawable.ic_switch_flight_mode), null);

        } else {
            mBinding.btnFlightMode.setText("离线模式");
            mBinding.btnFlightMode.setTextColor(getColorExt(R.color.mission_mode_btn_disconnected_border));
            mBinding.btnFlightMode.setBackgroundDrawable(getDrawableExt(R.drawable.switch_flight_mode_offline_bg));
            mBinding.btnFlightMode.setCompoundDrawablesWithIntrinsicBounds(null, null, getDrawableExt(R.drawable.ic_switch_flight_mode_offline), null);
        }
    }

    /**
     * 检查登录状态，决定是否需要连接MQ
     */
    private void checkAndInitMQConnection() {
        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache != null && !mqttConnectionInitialized) {
            // 用户已登录且MQ未初始化，标记为需要连接
            mqttConnectionInitialized = true;
            XLogUtil.INSTANCE.d(TAG, "用户已登录，标记MQ连接为待初始化状态");
        } else if (loginCache == null) {
            // 用户未登录，确保MQ不会连接
            mqttConnectionInitialized = false;
            XLogUtil.INSTANCE.d(TAG, "用户未登录，确保MQ连接为关闭状态");
        }
    }

    /**
     * 同步MQ连接状态与登录状态
     */
    private void syncMQConnectionWithLoginState() {
        LoginCache loginCache = SpUtil.getLoginCache();
        if (loginCache == null) {
            // 用户未登录，确保MQ断开
            if (MQttManager.getInstance().isConnected()) {
                XLogUtil.INSTANCE.d(TAG, "用户未登录但MQ仍连接，断开MQ连接");
                MQttManager.getInstance().closeMQtt();
            }
            mqttConnectionInitialized = false;
        } else if (currentSN != null && !MQttManager.getInstance().isConnected() && mqttConnectionInitialized) {
            // 用户已登录且有SN，但MQ未连接，尝试连接
            XLogUtil.INSTANCE.d(TAG, "用户已登录且有SN但MQ未连接，尝试连接MQ");
            MQttManager.getInstance().safeConnectMQtt(currentSN);
        }
    }

    /**
     * 检查是否应该更新站点位置
     * @return true 如果满足更新条件，false 否则
     */
    private boolean shouldUpdateSiteLocation() {
        // 检查无人机连接状态
        if (!isConnect) {
            XLogUtil.INSTANCE.d(TAG, "无人机未连接，跳过站点位置更新");
            return false;
        }

        // 检查UAVInfoSN完整性
        UAVInfoSN uavInfoSN = DJIAircraftApplication.getInstance().getUavInfoSN();
        if (uavInfoSN == null) {
            XLogUtil.INSTANCE.d(TAG, "UAVInfoSN为空，跳过站点位置更新");
            return false;
        }

        if (uavInfoSN.getData() == null) {
            XLogUtil.INSTANCE.d(TAG, "UAVInfoSN数据为空，跳过站点位置更新");
            return false;
        }

        // 检查站点信息完整性
        if (uavInfoSN.getData().getSiteInfo() == null) {
            XLogUtil.INSTANCE.d(TAG, "站点信息为空，无人机可能未绑定站点，跳过位置更新");
            return false;
        }

        if (uavInfoSN.getData().getSiteInfo().getSiteID() == null ||
            uavInfoSN.getData().getSiteInfo().getSiteID().trim().isEmpty()) {
            XLogUtil.INSTANCE.d(TAG, "站点ID无效，跳过站点位置更新");
            return false;
        }

        if (uavInfoSN.getData().getSiteInfo().getSiteLocation() == null) {
            XLogUtil.INSTANCE.d(TAG, "站点位置信息为空，跳过站点位置更新");
            return false;
        }

        // 检查无人机位置
        LocationCoordinate2D currentLocation = KeyManager.getInstance().getValue(KeyTools.createKey(FlightControllerKey.KeyAircraftLocation));
        if (currentLocation == null) {
            XLogUtil.INSTANCE.d(TAG, "无人机当前位置无效，跳过站点位置更新");
            return false;
        }

        XLogUtil.INSTANCE.d(TAG, "站点位置更新条件检查通过 - 站点ID: " +
                           uavInfoSN.getData().getSiteInfo().getSiteID());
        return true;
    }

    /**
     * 检查是否可以立即更新（频率控制）
     * @return true 如果可以更新，false 如果更新过于频繁
     */
    private boolean canUpdateNow() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastUpdateTime < UPDATE_INTERVAL) {
            return false;
        }
        lastUpdateTime = currentTime;
        return true;
    }

}

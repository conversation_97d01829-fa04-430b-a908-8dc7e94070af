package dji.sampleV5.aircraft.net.bean;

import rxhttp.wrapper.annotation.DefaultDomain;

public class NetConfig {

    //正式服务器地址
    public static final boolean mqttTest = false;
    public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = false;
    public static final String PUBLIC_APP_SUFFIX = "publish";
    public static final String LGURl1 = "http://*************:8880/";
    public static final String LGURl2 = "http://*************:8880/V1.0/";
    public static final String LGURl3 = "http://*************:8880/v2/";//驭光2.0的接口-岚峰接口
    public static final String NJ_MEDIA = "http://*************:9200/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://*************:9003/";
    public static final String FREEINIT = "http://*************:9200/";
    public static final String WebUrl = "https://solar.skysys.cn:8080/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "https://skyscout.skysys.cn/api/v3/"; //祺云环境正式服

    //公网底座
    public static final String BASE_JQURl2 = "http://**********:7070/"; //底座环境正式服

    // 老李提供的融合接口
    @DefaultDomain
    public static final String MIXURL = "http://*************:9110/";


    public static final String QI_CLOUD = "http://*************:9088/"; //这个地址只有一处引用但是没有调用所以暂时不管
    public static final String QI_CLOUD_MEDIA = "https://skyscout.skysys.cn:9921/";
    public static final String MISSION = "http://47.101.68.104:8080/";
    //公网祺云环境
    public static final String MqttServerUrl = "tcp://mqtt-cn-0pp0s45s703.mqtt.aliyuncs.com:1883";
    public static final String MqttUserName = "LTAI4G93t1z9tXkGY6BJrkCk";
    public static final String MqttPwd = "";
    //公网底座环境
    public static final String BASE_MqttServerUrl = "tcp://**********:1883";
    public static final String BASE_MqttUserName = "mqtt";
    public static final String BASE_MqttPwd = "<EMAIL>";

    public static final String MqttTopicPublish = "toConsoleDebug";
    public static final String TOPIC_PREFIX = "GID_UAV@@@";

    public static final String FtpHost = "************";
    public static final int FtpPort = 2122;
    public static final String FtpName = "xlygftp";
    public static final String FtpPwd = "xlyg@ftp2023Go!";


    //MINIO配置
    public static final String MINIO_ENDPOINT = "http://**************:9199";
    public static final String MINIO_ACCESS_KEY = "skysys";
    public static final String MINIO_SECRET_KEY = "SkySysGo!2023";
    public static final String MINIO_BUCKET_NAME = "skysys-shangyun";
    public static final String MINIO_REGION = "us-east-1";

    //OSS配置
    public static final String OSS_ENDPOINT = "https://oss-cn-shanghai.aliyuncs.com";
    public static final String OSS_ACCESS_KEY = "LTAI5tLSJ1Jd91LeBLkeuQJx";
    public static final String OSS_SECRET_KEY = "******************************";
    public static final String OSS_BUCKET_NAME = "skysys-shangyun";
    public static final String OSS_REGION = "cn-shanghai";

    //永华内网测试
    /*public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = false;
    public static final String PUBLIC_APP_SUFFIX = "永华_publish";
    public static final String LGURl1 = "http://***********49:8303/";
    public static final String LGURl2 = "http://***********49:8303/V1.0/";
    public static final String LGURl3 = "http://***********49:8303/v2/";//驭光2.0的接口-岚峰接口
    public static final String NJ_MEDIA = "http://***********49:8307/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://***********49:8308/";
    public static final String FREEINIT = "http://***********49:8307/";
    public static final String WebUrl = "http://***********49:8311/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://***********49:8011/api/v3/"; //融合环境正式服

    // 老李提供的融合接口
    @DefaultDomain
    public static final String MIXURL = "http://***********49:8329/";


    public static final String QI_CLOUD = "http://***********49:8011/";
    //public static final String QI_CLOUD_MEDIA = "http://***********49:8011/";
    public static final String MISSION = "http://***********49:8011/";
    public static final String MqttServerUrl = "tcp://***********49:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "SkySys@2023";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "***********49";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "skysys@2023";
    public static final String TOPIC_PREFIX = "";*/

    //测试服务器地址
   /* public static final boolean mqttTest = true;
    public static final boolean isTest = true;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = false;
    public static final String PUBLIC_APP_SUFFIX = "test";
    public static final String LGURl1 = "http://**************:8880/V2/";
    public static final String LGURl2 = "http://**************:8181/V1.0/";
    public static final String LGURl3 = "http://**************:8181/v2/";//驭光2.0的接口

//    public static final String LGURl1 = "http://*************:9004/";
//    public static final String LGURl2 = "http://*************:9004/V1.0/";
//    public static final String LGURl3 = "http://*************:9321/v2/";//驭光2.0的接口-岚峰新接口

    public static final String NJ_MEDIA = "http://*************:9201/"; //南京多媒体服务测试服
    public static final String NJLOGIN3 = "http://*************:9004/";
    public static final String FREEINIT = "http://*************:9201/";
    public static final String WebUrl = "http://*************:8680/";
    public static final String PID = "yuguang-dev";*/
    //public static final String JQURl2 = "https://test.skysys.cn/api/v3/"; //融合环境测试服


    //达梦数据库
    /*@DefaultDomain
    public static final String MIXURL = "http://**************:9110/";
    public static final String PID = "yuguang-dev";
    public static final String PUBLIC_APP_SUFFIX = "达梦";
    public static final String NJLOGIN3 = "http://**************:9004/";
    public static final String FREEINIT = "http://**************:9201/";*/

    //本地环境
    /*public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final String LGURl1 = "http://*************:8303/V2/";
    public static final String LGURl2 = "http://*************:8303/V1.0/";
    public static final String LGURl3 = "http://*************:8303/v2/";//驭光2.0的接口 11 usages
    public static final String NJLOGIN3 = "http://*************:8308/";
    public static final String FREEINIT = "http://*************:8307/";
    public static final String WebUrl = "http://*************:8311/";
    public static final String PID = "yuguang";
    public static final String JQURl2 ="http://*************:8001/api/v3/";

    @DefaultDomain
    public static final String MIXURL = "http://*************:8329/";
    public static final String QI_CLOUD = "http://*************:8001/";
    public static final String QI_CLOUD_MEDIA = "http://*************:8001/";
    public static final String MISSION = "http://*************:8001/";
    public static final String MqttServerUrl = "tcp://*************:1883";
    public static final String MqttUserName = "skysys";
    public static final String MqttPwd = "SkySys@2023";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "*************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";
    public static final String TOPIC_PREFIX = "";*/


    public static final int SUCCESS_CODE = 0;
    public static final int NEW_SUCCESS_CODE = 200;

    //东大金智
    /*public static final boolean mqttTest = false;
    public static final boolean isTest = false;
    public static final boolean isSocketHttps = false;
    public static final boolean isInnerNet = true;
    public static final String PUBLIC_APP_SUFFIX = "东大金智_private";
    public static final String LGURl1 = "http://*************:8303";
    public static final String LGURl2 = "http://*************:8303/V1.0/";
    public static final String LGURl3 = "http://*************:8303/v2/";//驭光2.0的接口-岚峰接口
    public static final String NJ_MEDIA = "http://*************:8307/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://*************:8308/";
    public static final String FREEINIT = "http://*************:8307/";
    public static final String WebUrl = "http://*************:9126/yg";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://*************:7070/api/v3/"; //融合环境正式服

    // 老李提供的融合接口
    @DefaultDomain
    public static final String MIXURL = "http://*************:8329/";


    public static final String QI_CLOUD = "http://*************:9088/"; //这个地址只有一处引用但是没有调用所以暂时不管
    public static final String QI_CLOUD_MEDIA = "http://*************:7070";
    public static final String MISSION = "http://*************:7070";
    public static final String MqttServerUrl = "tcp://*************:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "public";
    public static final String MqttTopicPublish = "toConsole";
    public static final String TOPIC_PREFIX = "GID_UAV@@@";

    public static final String FtpHost = "************";
    public static final int FtpPort = 2122;
    public static final String FtpName = "xlygftp";
    public static final String FtpPwd = "xlyg@ftp2023Go!";


    //MINIO配置
    public static final String MINIO_ENDPOINT = "http://*************:9199";
    public static final String MINIO_ACCESS_KEY = "skysys";
    public static final String MINIO_SECRET_KEY = "SkySys@2023";
    public static final String MINIO_BUCKET_NAME = "skysys-shangyun";
    public static final String MINIO_REGION = "us-east-1";*/


    //数研院
    //正式服务器地址
    /*public static final boolean mqttTest = false;
    public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = false;
    public static final String PUBLIC_APP_SUFFIX = "数研院_publish";
    public static final String LGURl1 = "http://*************:8303/V2/";
    public static final String LGURl2 = "http://*************:8303/V1.0/";
    public static final String LGURl3 = "http://*************:8303/v2/";//驭光2.0的接口
    public static final String NJ_MEDIA = "http://*************:8303/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://*************:8308/";
    public static final String FREEINIT = "http://*************:8307/";
    public static final String WebUrl = "http://*************:8311/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://*************:8011/api/v3/"; //融合环境正式服

    @DefaultDomain
    public static final String MIXURL = "http://*************:8329/";

    public static final String QI_CLOUD = "http://*************:8011/";
    public static final String QI_CLOUD_MEDIA = "http://*************:8011/";
    public static final String MISSION = "http://*************:8011/";
    public static final String MqttServerUrl = "tcp://*************:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "public";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "*************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";
    public static final String TOPIC_PREFIX = "GID_UAV@@@";*/

    //浙江图盛
    //正式服务器地址
    /*public static final boolean mqttTest = false;
    public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = true;
    public static final String PUBLIC_APP_SUFFIX = "浙江图盛_publish";
    public static final String LGURl1 = "http://**************:8303/";
    public static final String LGURl2 = "http://**************:8303/V1.0/";
    public static final String LGURl3 = "http://**************:8303/v2/";//驭光2.0的接口-岚峰接口
    public static final String NJ_MEDIA = "http://**************:8307/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://**************:8308/";
    public static final String FREEINIT = "http://**************:8307/";
    public static final String WebUrl = "http://**************:9126/yg/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://**************:9126/api/v3/"; //融合环境正式服

    // 老李提供的融合接口
    @DefaultDomain
    public static final String MIXURL = "http://**************:8329/";


    public static final String QI_CLOUD = "http://**************:9126/"; //这个地址只有一处引用但是没有调用所以暂时不管
    public static final String QI_CLOUD_MEDIA = "http://**************:9126/";
    public static final String MISSION = "http://**************:9126/";
    public static final String MqttServerUrl = "tcp://**************:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "SkySys@2023";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "**************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";
    public static final String TOPIC_PREFIX = "";*/

    //华能河北上安
    /*public static final boolean mqttTest = false;
    public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = true;
    public static final String PUBLIC_APP_SUFFIX = "河北上安_publish";
    public static final String LGURl1 = "http://**************:8303/";
    public static final String LGURl2 = "http://**************:8303/V1.0/";
    public static final String LGURl3 = "http://**************:8303/v2/";//驭光2.0的接口-岚峰接口
    public static final String NJ_MEDIA = "http://**************:8307/"; //南京多媒体服务正式服
    public static final String NJLOGIN3 = "http://**************:8308/";
    public static final String FREEINIT = "http://**************:8307/";
    public static final String WebUrl = "http://**************:8311/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://**************:8011/api/v3/"; //融合环境正式服

    // 老李提供的融合接口
    @DefaultDomain
    public static final String MIXURL = "http://**************:8329/";


    public static final String QI_CLOUD = "http://**************:9088/";
    //public static final String QI_CLOUD_MEDIA = "https://**************:9921/";
    public static final String MISSION = "http://**************:8011/";
    public static final String MqttServerUrl = "tcp://**************:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "SkySys@2023";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "**************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";
    public static final String TOPIC_PREFIX = "GID_UAV@@@";*/

    //天龙项目
    //正式服务器地址
    /*public static final boolean isTest = false;
    public static final boolean isSocketHttps = true;
    public static final boolean isInnerNet = true;
    public static final String PUBLIC_APP_SUFFIX = "天龙";
    public static final String LGURl1 = "http://************:8303/V2/";
    public static final String LGURl2 = "http://************:8303/V1.0/";
    public static final String LGURl3 = "http://************:8303/v2/";//驭光2.0的接口
    public static final String NJLOGIN3 = "http://************:8308/";
    public static final String FREEINIT = "http://************:8307/";
    public static final String WebUrl = "http://************:8311/";
    public static final String PID = "yuguang";
    public static final String JQURl2 = "http://************:8011/api/v3/"; //融合环境正式服

    @DefaultDomain
    public static final String MIXURL = "http://************:8329/";
    public static final String QI_CLOUD = "http://************:8011/";
    public static final String QI_CLOUD_MEDIA = "http://************:9011/";
    public static final String MISSION = "http://************:8011/";
    public static final String MqttServerUrl = "tcp://************:1883";
    public static final String MqttUserName = "LTAI4G93t1z9tXkGY6BJrkCk";
    public static final String MqttPwd = "";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";
    public static final String TOPIC_PREFIX = "";*/



    //安徽清新懿能
    /*public static final boolean isTest = false;
    public static final boolean isSocketHttps = false;
    public static final String PUBLIC_APP_SUFFIX = "清新懿能";
    public static final String LGURl1 = "http://************7:8303/V2/";
    public static final String LGURl2 = "http://************7:8303/V1.0/";
    public static final String LGURl3 = "http://************7:8303/v2/";
    public static final String FREEINIT = "http://************7:8307/";
    public static final String WebUrl = "http://************7:8311/";
    public static final String TOPIC_PREFIX = "";

    public static final int SUCCESS_CODE = 0;
    public static final int NEW_SUCCESS_CODE = 200;
    public static final String NJLOGIN3 = "http://************7:8308/";

    @DefaultDomain
    public static final String MIXURL = "http://************7:8329/";
    public static final String PID = "yuguang";

    public static final String QI_CLOUD = "http://*************:9088/";  //这个没用了，但是代码里引用了，先放着
    public static final String QI_CLOUD_MEDIA = "http://************7:8309/";

    public static final String JQURl2 = "http://************7:8309/api/v3/";

    public static final String MISSION = "http://************7:8309/";

    public static final String MqttServerUrl = "tcp://************7:8304";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "public";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "************7";
    public static final int FtpPort = 8302;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "qiyun2021";*/

    //湖南工业内网
   /* public static final String WebUrl = "http://**********:9090/";
    public static final String LGURl1 = "http://**********:8880/V2/";
    public static final String LGURl2 = "http://**********:8880/V1.0/";
    public static final String NJLOGIN = "http://**********:9001/";
    public static final String JQURl1 = "http://**********:20219/qcp/";
    public static final String JQURl2 = "http://**********:20219/api/v1/";
    public static final String MqttServerUrl = "tcp://**********:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "public";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "**********";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "qiyun2021";
    public static final String QI_CLOUD = "http://**********:9088/";
    public static final String QI_CLOUD_MEDIA = "http://**********/";
    public static final String MISSION = "http://**********/";
    public static final String YG_FORMAL = "http://**********:9980/";*/

    //永华临港测试
    /*public static final boolean isTest = false;
    public static final boolean isSocketHttps = false;
    public static final String PUBLIC_APP_SUFFIX = "永华临港测试";
    public static final String LGURl1 = "http://***********52:8303/V2/";
    public static final String LGURl2 = "http://***********52:8303/V1.0/";
    public static final String LGURl3 = "http://***********52:8303/v2/";
    public static final String FREEINIT = "http://***********52:8307/";
    public static final String WebUrl = "http://***********52:8311/";
    public static final String NJLOGIN3 = "http://***********52:8308/";
    public static final int SUCCESS_CODE = 0;
    public static final int NEW_SUCCESS_CODE = 200;
    @DefaultDomain
    public static final String MIXURL = "http://***********52:8329/";
    public static final String PID = "yuguang";
    public static final String TOPIC_PREFIX = "";
    public static final String QI_CLOUD = "http://*************:9088/";  //这个没用了，但是代码里引用了，先放着
    public static final String QI_CLOUD_MEDIA = "http://***********52:8001/";

    public static final String JQURl2 = "http://***********52:8001/api/v3/";

    public static final String MISSION = "http://***********52:8001/";

    public static final String MqttServerUrl = "tcp://***********52:1883";
    public static final String MqttUserName = "admin";
    public static final String MqttPwd = "public";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "***********52";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "SkySys@2023";*/

    //泗洪内网
  /*  public static final String WebUrl = "http://*************:9090/";
    public static final String LGURl1 = "http://*************:8880/V2/";
    public static final String LGURl2 = "http://*************:8880/V1.0/";
    public static final String JQURl1 = "http://*************:20219/qcp/";
    public static final String JQURl2 = "http://*************:20219/api/v1/";
    public static final String MqttServerUrl = "tcp://*************:1883";
    public static final String MqttUserName = "root";
    public static final String MqttPwd = "SkySysGo!";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "*************";
    public static final int FtpPort = 10021;
    public static final String FtpName = "skysys";
    public static final String FtpPwd = "qiyun2021";*/

    //王瑞测试
    /*
     public static final String WebUrl = "http://*************:8680/";
    public static final String LGURl1 = "http://*************:8880/V2/";
    public static final String LGURl2 = "http://*************:8880/V1.0/";
    public static final String JQURl1 = "http://*************:9901/qcp/";
    public static final String JQURl2 = "http://*************:20219/api/v1/";
    public static final String MqttServerUrl = "tcp://*************:1883";
    public static final String MqttUserName = "root";
    public static final String MqttPwd = "SkySysGo!";
    public static final String MqttTopicPublish = "toConsole";
    public static final String FtpHost = "***************";
    public static final int FtpPort = 58021;
    public static final String FtpName = "uav";
    public static final String FtpPwd = "uav";*/

    /*登录 "http://*************:8880/V2/aIDriverLightUser/login
        任务下发 http://*************:9901/qcp/uavaction/missionadd
        获取站点信息https://skyscout.skysys.cn:9921/skysys/api/v1/uav/fcsns/" + sn + "/uavinfo
        上报成功通知谢工http://*************:8880/V1.0/UAV/Picture/Upload/Record 还要加token
        获取所有的场站信息http://*************:8880/V1.0/AI/Photovo/User/Location/List/test 还要加token
        消缺的信息上报http://*************:8880/V1.0/AI/Photovo/Delete/Defect/" + defect + "/" + missionbatch + "/" + StrID + "/test"
        Mqtt的连接信息
        FTP服务器的连接信息*/
}

package dji.sampleV5.aircraft.mvvm.ui.activity.site

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import dji.sampleV5.aircraft.databinding.ActivityRegisterStationBinding
import dji.sampleV5.aircraft.mvvm.ui.viewModel.StationRegistrationViewModel
import dji.sampleV5.aircraft.mvvm.ui.viewModel.DroneBindingMode
import dji.sampleV5.aircraft.mvvm.ui.viewModel.RegistrationState
import dji.sampleV5.aircraft.mvvm.ui.viewModel.FormValidationState
import dji.sampleV5.aircraft.mvvm.net.response.UnbindUavBean
import dji.sampleV5.aircraft.mvvm.net.response.DeviceModelBeanX
import dji.sampleV5.aircraft.mvvm.net.request.RegisterSiteRequest
import dji.sampleV5.aircraft.mvvm.net.request.RegisterUavRequest
import dji.sampleV5.aircraft.R
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.model.*
import com.amap.api.location.AMapLocation
import com.amap.api.location.AMapLocationClient
import com.amap.api.location.AMapLocationClientOption
import com.amap.api.location.AMapLocationListener
import android.Manifest
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import dji.sampleV5.aircraft.mvvm.util.XLogUtil
import com.hjq.toast.Toaster
import dji.sampleV5.aircraft.mvvm.base.appContext
import dji.sampleV5.aircraft.mqtt.MQttManager
import dji.sampleV5.aircraft.util.StorageExtKt
import dji.sampleV5.aircraft.util.ValueKey
import dji.v5.manager.KeyManager
import dji.v5.manager.interfaces.CommonCallbacks
import dji.v5.utils.common.KeyTools
import dji.sdk.keyvalue.key.FlightControllerKey
import dji.sampleV5.aircraft.mvvm.ui.viewModel.AutoFillState
import dji.sampleV5.aircraft.mvvm.ui.viewModel.DJIConnectionState
import android.graphics.Rect
import android.util.TypedValue
import android.view.ViewTreeObserver
import dji.sampleV5.aircraft.mvvm.base.BaseVMActivity

class RegisterStationActivity : BaseVMActivity<StationRegistrationViewModel, ActivityRegisterStationBinding>(), AMapLocationListener {

    // 地图相关属性
    private lateinit var aMap: AMap
    private lateinit var mapView: MapView
    private var locationClient: AMapLocationClient? = null
    private var currentLocationMarker: Marker? = null

    // 防抖处理Handler
    private val updateHandler = Handler(Looper.getMainLooper())
    private var updateRunnable: Runnable? = null

    // 防止UI更新时光标跳转的标志
    private var isUserInputting = false

    // 权限请求码
    private val LOCATION_PERMISSION_REQUEST_CODE = 1001

    // 新建无人机数据缓存
    private var cachedNewDroneData: CachedDroneData? = null

    // 键盘监听相关
    private var keyboardLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var isKeyboardVisible = false

    /**
     * 新建无人机数据缓存类
     */
    private data class CachedDroneData(
        val uavName: String = "",
        val uavId: String = "",
        val uavSn: String = "",
        val brand: String = "",
        val model: String = ""
    )

    override fun initView(savedInstanceState: Bundle?) {

        // 初始化绑定模式选择按钮
        initBindingModeButtons()

        // 设置观察者
        setupObservers()

        // 设置实时监听器
        setupRealTimeListeners()

        // 初始化确定按钮状态（默认禁用）
        initConfirmButtonState()

        // 初始化默认状态
        initDefaultState(savedInstanceState)
    }

    override fun initData() {
        // 加载未绑定设备列表（为切换到已有设备模式做准备）
        mViewModel.loadUnbindDeviceList()

        // 初始化下拉选择器数据
        initializeSpinners()

        // 加载设备型号列表（为型号选择做准备）
        mViewModel.loadDeviceModelList()

        // 触发初始验证，确保确定按钮状态正确
        mViewModel.validateAllFields()
    }


    /**
     * 初始化绑定模式选择按钮
     */
    private fun initBindingModeButtons() {
        //返回
        mBinding.ivTitle.setOnClickListener {
            finish()
        }
        // 绑定新建无人机按钮点击事件
        mBinding.tvBindNewDrone.setOnClickListener {
            selectBindingMode(DroneBindingMode.NEW_DRONE)
        }

        // 绑定已有无人机按钮点击事件
        mBinding.tvBindExistingDrone.setOnClickListener {
            selectBindingMode(DroneBindingMode.EXISTING_DRONE)
        }
    }

    /**
     * 选择绑定模式
     */
    private fun selectBindingMode(mode: DroneBindingMode) {

        // 获取当前模式，用于判断是否需要缓存数据
        val currentMode = mViewModel.droneBindingMode.value

        // 根据模式切换处理数据
        when (mode) {
            DroneBindingMode.NEW_DRONE -> {
                // 切换到新建无人机模式
                if (currentMode == DroneBindingMode.EXISTING_DRONE) {
                    // 【修复】先清空已有设备数据，再恢复缓存的新建无人机数据
                    clearExistingDeviceData()
                    restoreNewDroneData()
                } else {
                    // 如果不是从已有设备模式切换过来，也要清空已有设备数据
                    clearExistingDeviceData()
                }
                // 所有字段可编辑
                setExistingDeviceFieldsEditable(true)
            }
            DroneBindingMode.EXISTING_DRONE -> {
                // 切换到已有设备模式
                if (currentMode == DroneBindingMode.NEW_DRONE) {
                    // 从新建模式切换过来，缓存当前新建无人机数据
                    cacheNewDroneData()
                    // 清空新建无人机的UI数据
                    clearNewDroneUIData()
                }
            }
        }


        // 更新ViewModel状态
        mViewModel.setDroneBindingMode(mode)

        // 更新按钮UI状态
        updateBindingModeUI(mode)

        // 切换输入组件
        switchInputComponent(mode == DroneBindingMode.NEW_DRONE)
    }

    /**
     * 更新绑定模式按钮的UI状态
     */
    private fun updateBindingModeUI(mode: DroneBindingMode) {
        when (mode) {
            DroneBindingMode.NEW_DRONE -> {
                // 新建无人机按钮选中状态
                mBinding.tvBindNewDrone.setBackgroundResource(R.drawable.selector_add_new_mission_confirm_bg)
                mBinding.tvBindNewDrone.setTextColor(resources.getColor(R.color.white, null))

                // 已有无人机按钮未选中状态
                mBinding.tvBindExistingDrone.setBackgroundResource(R.drawable.selector_add_new_mission_cancel_bg_focused)
                mBinding.tvBindExistingDrone.setTextColor(resources.getColor(R.color.item_btn_start, null))
            }
            DroneBindingMode.EXISTING_DRONE -> {
                // 新建无人机按钮未选中状态
                mBinding.tvBindNewDrone.setBackgroundResource(R.drawable.selector_add_new_mission_cancel_bg_focused)
                mBinding.tvBindNewDrone.setTextColor(resources.getColor(R.color.item_btn_start, null))

                // 已有无人机按钮选中状态
                mBinding.tvBindExistingDrone.setBackgroundResource(R.drawable.selector_add_new_mission_confirm_bg)
                mBinding.tvBindExistingDrone.setTextColor(resources.getColor(R.color.white, null))
            }
        }
    }

    /**
     * 切换输入组件显示
     */
    private fun switchInputComponent(showEditText: Boolean) {
        if (showEditText) {
            // 显示输入框，隐藏下拉框
            mBinding.etUavName.apply {
                visibility = View.VISIBLE
                alpha = 0f
                animate().alpha(1f).setDuration(200).start()
            }
            mBinding.esUavName.apply {
                animate().alpha(0f).setDuration(200)
                    .withEndAction { visibility = View.GONE }
                    .start()
            }
        } else {
            // 显示下拉框，隐藏输入框
            mBinding.esUavName.apply {
                visibility = View.VISIBLE
                alpha = 0f
                animate().alpha(1f).setDuration(200).start()
            }
            mBinding.etUavName.apply {
                animate().alpha(0f).setDuration(200)
                    .withEndAction { visibility = View.GONE }
                    .start()
            }
        }
    }

    /**
     * 初始化确定按钮状态（默认禁用）
     */
    private fun initConfirmButtonState() {
        // 默认设置为禁用状态
        updateConfirmButtonState(FormValidationState(isValid = false))
    }

    /**
     * 更新确定按钮状态
     */
    private fun updateConfirmButtonState(validationState: FormValidationState) {
        val isEnabled = validationState.isValid
        mBinding.tvConfirm.isEnabled = isEnabled

        // 只在状态发生变化时打印日志，避免频繁打印
        if (mBinding.tvConfirm.isEnabled != isEnabled) {
            XLogUtil.d("RegisterStation",
                "确定按钮状态更新: isEnabled=$isEnabled")
        }
    }

    /**
     * 设置观察者
     */
    private fun setupObservers() {
        // 观察绑定模式变化
        mViewModel.droneBindingMode.observe(this) { mode ->
            updateBindingModeUI(mode)
            switchInputComponent(mode == DroneBindingMode.NEW_DRONE)
        }

        // 观察未绑定设备列表
        mViewModel.unbindDeviceList.observe(this) { deviceList ->
            setupDeviceSpinner(deviceList)
        }

        // 观察选中的已有设备
        mViewModel.selectedExistingDevice.observe(this) { device ->
            device?.let {
                // 当选择已有设备时，填充所有相关信息到UI
                fillExistingDeviceDataToUI(it)
            }
        }

        // 观察站点数据变化
        mViewModel.stationData.observe(this) { stationData ->
            updateStationDataUI(stationData)
        }

        // 观察无人机数据变化
        mViewModel.droneData.observe(this) { droneData ->
            updateDroneDataUI(droneData)
        }

        // 观察设备型号列表
        mViewModel.deviceModelList.observe(this) { modelList ->
            setupDeviceModelSpinner(modelList)
        }

        // 观察表单验证状态
        mViewModel.validationState.observe(this) { validationState ->
            updateConfirmButtonState(validationState)
        }

        // 观察注册状态
        mViewModel.registrationState.observe(this) { state ->
            handleRegistrationState(state)
        }

        // 观察表单验证状态
        mViewModel.validationState.observe(this) { validationState ->
            updateValidationUI(validationState)
        }

        // 观察DJI连接状态
        mViewModel.djiConnectionState.observe(this) { connectionState ->
            handleDJIConnectionState(connectionState)
        }

        // 观察DJI自动填充状态
        mViewModel.autoFillState.observe(this) { autoFillState ->
            handleAutoFillState(autoFillState)
        }
    }

    /**
     * 设置设备下拉选择器
     */
    private fun setupDeviceSpinner(deviceList: List<UnbindUavBean>) {
        if (deviceList.isNotEmpty()) {
            val deviceNames = deviceList.map { it.uavName }
            mBinding.esUavName.setItems(deviceNames)

            // 设置选择监听器
            mBinding.esUavName.setOnItemClickListener { _, _, position, _ ->
                if (position >= 0 && position < deviceList.size) {
                    val selectedDevice = deviceList[position]
                    mViewModel.selectExistingDevice(selectedDevice)
                }
            }
        } else {
            // 如果设备列表为空，清空下拉选择器
            mBinding.esUavName.setItems(emptyList())
            XLogUtil.w("RegisterStation", "未绑定设备列表为空")
        }
    }

    /**
     * 初始化默认状态
     */
    private fun initDefaultState(savedInstanceState: Bundle?) {
        // 默认选择新建无人机模式
        selectBindingMode(DroneBindingMode.NEW_DRONE)

        // 设置UI组件的事件监听器
        setupUIListeners()

        // 初始化地图
        initMap(savedInstanceState)

        // 初始化键盘监听
        setupKeyboardListener()

        // 设置输入框焦点监听
        setupInputFocusListeners()
    }

    /**
     * 防抖更新方法
     */
    private fun debounceUpdate(action: () -> Unit, delay: Long = 300) {
        updateRunnable?.let { updateHandler.removeCallbacks(it) }
        updateRunnable = Runnable { action() }
        updateHandler.postDelayed(updateRunnable!!, delay)
    }

    /**
     * 设置实时监听器 - 使用TextWatcher实现实时数据同步
     */
    private fun setupRealTimeListeners() {
        // 站点名称实时监听
        mBinding.etStationName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val name = s.toString()
                debounceUpdate(action = { mViewModel.updateSiteName(name) })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 返航高度实时监听
        mBinding.etReturnHeight.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val altitude = s.toString()
                isUserInputting = true
                debounceUpdate(action = {
                    mViewModel.updateReturnHomeAltitude(altitude)
                    // 延迟重置标志，确保UI更新完成后才允许下一次更新
                    updateHandler.postDelayed({ isUserInputting = false }, 500)
                })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 椭球高度实时监听
        mBinding.etEllipsoidHeight.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val altitude = s.toString()
                isUserInputting = true
                debounceUpdate(action = {
                    mViewModel.updateEllipsoidAltitude(altitude)
                    // 延迟重置标志，确保UI更新完成后才允许下一次更新
                    updateHandler.postDelayed({ isUserInputting = false }, 500)
                })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 无人机名称实时监听（新建模式）
        mBinding.etUavName.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val name = s.toString()
                debounceUpdate(action = { mViewModel.updateDroneName(name) })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 无人机编号实时监听
        mBinding.etUavNumber.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val uavId = s.toString()
                debounceUpdate(action = {
                    mViewModel.updateDroneId(uavId)
                })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 无人机FCSN实时监听
        mBinding.etUavFcSn.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val fcSn = s.toString()
                debounceUpdate(action = {
                    mViewModel.updateDroneSerialNumber(fcSn)
                })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 生产商实时监听
        mBinding.etUavFcProducer.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val brand = s.toString()
                debounceUpdate(action = { mViewModel.updateDroneBrand(brand) })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 实时播流配置实时监听
        mBinding.etRealVideoConfig.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val address = s.toString()
                debounceUpdate(action = { mViewModel.updateVideoPullAddress(address) })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        // 推流配置实时监听
        mBinding.etRealVideoPush.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                val address = s.toString()
                debounceUpdate(action = { mViewModel.updateVideoPushAddress(address) })
            }
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })
    }

    /**
     * 设置UI组件的事件监听器（保留焦点监听作为备用）
     */
    private fun setupUIListeners() {
        // 站点名称输入监听
        mBinding.etStationName.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val name = mBinding.etStationName.text.toString()
                mViewModel.updateSiteName(name)
            }
        }

        // 飞行模式选择监听
        mBinding.spFlyMode.setOnItemClickListener { _, _, position, _ ->
            val selectedMode = mViewModel.flightModeOptions[position]
            mViewModel.updateFlightMode(selectedMode)
        }

        // 配置模式选择监听
        mBinding.spConfigMode.setOnItemClickListener { _, _, position, _ ->
            val selectedMode = mViewModel.configModeOptions[position]
            mViewModel.updateConfigMode(selectedMode)
        }

        // 返航高度输入监听
        mBinding.etReturnHeight.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val altitude = mBinding.etReturnHeight.text.toString()
                mViewModel.updateReturnHomeAltitude(altitude)
            }
        }

        // 椭球高度输入监听
        mBinding.etEllipsoidHeight.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val altitude = mBinding.etEllipsoidHeight.text.toString()
                mViewModel.updateEllipsoidAltitude(altitude)
            }
        }

        // 无人机名称输入监听（新建模式）
        mBinding.etUavName.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val name = mBinding.etUavName.text.toString()
                mViewModel.updateDroneName(name)
            }
        }

        // 无人机FCSN输入监听
        mBinding.etUavFcSn.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val fcSn = mBinding.etUavFcSn.text.toString()
                mViewModel.updateDroneSerialNumber(fcSn)
            }
        }

        // 生产商输入监听
        mBinding.etUavFcProducer.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val brand = mBinding.etUavFcProducer.text.toString()
                mViewModel.updateDroneBrand(brand)
            }
        }

        // 无人机型号选择监听
        mBinding.esUavModel.setOnItemClickListener { _, _, position, _ ->
            val modelList = mViewModel.deviceModelList.value
            if (!modelList.isNullOrEmpty() && position < modelList.size) {
                val selectedModel = modelList[position]
                mViewModel.updateDroneModelByCode(selectedModel.modelCode)
            }
        }

        // 实时播流配置输入监听
        mBinding.etRealVideoConfig.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val address = mBinding.etRealVideoConfig.text.toString()
                mViewModel.updateVideoPullAddress(address)
            }
        }

        // 推流配置输入监听
        mBinding.etRealVideoPush.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                val address = mBinding.etRealVideoPush.text.toString()
                mViewModel.updateVideoPushAddress(address)
            }
        }

        // 确认按钮点击监听
        mBinding.tvConfirm.setOnClickListener {
            // 只有在按钮启用状态下才执行注册
            if (mBinding.tvConfirm.isEnabled) {
                val validationState = mViewModel.validationState.value
                if (validationState?.isValid == true) {
                    mViewModel.performRegistration()
                } else {
                    XLogUtil.w("RegisterStation", "表单验证未通过，无法提交注册")
                    // 可以在这里显示具体的错误提示
                    validationState?.errors?.forEach { (field, error) ->
                        XLogUtil.w("RegisterStation", "字段错误: $field -> $error")
                    }
                }
            } else {
                XLogUtil.w("RegisterStation", "确定按钮处于禁用状态，无法点击")
            }
        }

        // 取消按钮点击监听
        mBinding.tvCancel.setOnClickListener {
            finish()
        }
    }

    /**
     * 初始化下拉选择器数据
     */
    private fun initializeSpinners() {
        // 初始化飞行模式下拉选择器
        mBinding.spFlyMode.setItems(mViewModel.flightModeOptions)
        mBinding.spFlyMode.setText(mViewModel.getCurrentFlightModeText())

        // 初始化配置模式下拉选择器
        mBinding.spConfigMode.setItems(mViewModel.configModeOptions)
        mBinding.spConfigMode.setText(mViewModel.getCurrentConfigModeText())
    }

    /**
     * 更新站点数据到UI - 增强版本，确保数据同步
     */
    private fun updateStationDataUI(stationData: RegisterSiteRequest) {
        // 更新站点名称 - 避免循环更新
        if (mBinding.etStationName.text.toString() != stationData.siteName) {
            mBinding.etStationName.setText(stationData.siteName)
        }

        // 更新返航高度 - 只在用户未输入时更新，避免光标跳转
        if (!isUserInputting) {
            val returnHeightText = stationData.siteRHAltitude?.toString() ?: ""
            if (mBinding.etReturnHeight.text.toString() != returnHeightText) {
                mBinding.etReturnHeight.setText(returnHeightText)
            }
        }

        // 更新椭球高度 - 只在用户未输入时更新，避免光标跳转
        if (!isUserInputting) {
            val ellipsoidHeightText = stationData.siteEllipsAltitude?.toString() ?: ""
            if (mBinding.etEllipsoidHeight.text.toString() != ellipsoidHeightText) {
                mBinding.etEllipsoidHeight.setText(ellipsoidHeightText)
            }
        }

        // 更新拉流地址
        val pullAddress = stationData.videoPullAddr ?: ""
        if (mBinding.etRealVideoConfig.text.toString() != pullAddress) {
            mBinding.etRealVideoConfig.setText(pullAddress)
        }

        // 更新推流地址
        val pushAddress = stationData.videoPushAddr ?: ""
        if (mBinding.etRealVideoPush.text.toString() != pushAddress) {
            mBinding.etRealVideoPush.setText(pushAddress)
        }

        // 更新飞行模式
        val flightMode = when (stationData.siteFlightMode) {
            1 -> "孤岛模式"
            2 -> "跳棋模式"
            else -> "孤岛模式"
        }
        if (mBinding.spFlyMode.text.toString() != flightMode) {
            mBinding.spFlyMode.setText(flightMode)
        }

        // 更新配置模式
        val configMode= when (stationData.siteMode) {
            4 -> "遥控器"
            5 -> "机载计算机"
            else -> "遥控器"
        }
        if (mBinding.spConfigMode.text.toString() != configMode) {
            mBinding.spConfigMode.setText(configMode)
        }

        // 更新位置信息
        if (stationData.siteLocation.isNotEmpty() && stationData.siteLocation.size >= 2) {
            val longitude = stationData.siteLocation[0]
            val latitude = stationData.siteLocation[1]
            mBinding.etLocation.setText("$latitude, $longitude")
        }
    }

    /**
     * 更新无人机数据到UI - 增强版本，确保数据同步
     */
    private fun updateDroneDataUI(droneData: RegisterUavRequest) {
        // 只在新建无人机模式下更新UI，避免与已有设备数据冲突
        if (mViewModel.droneBindingMode.value == DroneBindingMode.NEW_DRONE) {
            // 更新无人机名称 - 避免循环更新
            val uavName = droneData.uavName ?: ""
            if (mBinding.etUavName.text.toString() != uavName) {
                mBinding.etUavName.setText(uavName)
            }

            // 更新无人机编号
            val uavId = droneData.uavId ?: ""
            if (mBinding.etUavNumber.text.toString() != uavId) {
                mBinding.etUavNumber.setText(uavId)
            }

            // 更新序列号
            val sn = droneData.sn ?: ""
            if (mBinding.etUavFcSn.text.toString() != sn) {
                mBinding.etUavFcSn.setText(sn)
            }

            // 更新品牌
            val brand = droneData.brand ?: ""
            if (mBinding.etUavFcProducer.text.toString() != brand) {
                mBinding.etUavFcProducer.setText(brand)
            }

            // 更新型号（需要转换为显示名称）
            val modelName = getModelNameByCode(droneData.model)
            if (modelName != null && mBinding.esUavModel.text.toString() != modelName) {
                mBinding.esUavModel.setText(modelName)
            }

            // 更新缓存数据（DJI自动填充的数据也需要缓存）
            updateCachedNewDroneData()

            XLogUtil.d("RegisterStation",
                "无人机数据UI已更新: 名称=$uavName, 编号=$uavId, 序列号=$sn, 品牌=$brand, 型号=$modelName")
        }
    }

    /**
     * 更新缓存的新建无人机数据
     */
    private fun updateCachedNewDroneData() {
        cachedNewDroneData = CachedDroneData(
            uavName = mBinding.etUavName.text.toString().trim(),
            uavId = mBinding.etUavNumber.text.toString().trim(),
            uavSn = mBinding.etUavFcSn.text.toString().trim(),
            brand = mBinding.etUavFcProducer.text.toString().trim(),
            model = mBinding.esUavModel.text.toString().trim()
        )
    }

    /**
     * 设置设备型号下拉选择器
     */
    private fun setupDeviceModelSpinner(modelList: List<DeviceModelBeanX>) {
        if (modelList.isNotEmpty()) {
            val modelNames = modelList.map { it.modelName }
            mBinding.esUavModel.setItems(modelNames)
        }
    }

    /**
     * 根据型号代码获取显示名称
     */
    private fun getModelNameByCode(modelCode: String): String? {
        val modelList = mViewModel.deviceModelList.value
        return modelList?.find { it.modelCode == modelCode }?.modelName
    }

    /**
     * 处理注册状态变化
     */
    private fun handleRegistrationState(state: RegistrationState) {
        when (state) {
            is RegistrationState.Idle -> {
                // 空闲状态，隐藏加载指示器
            }
            is RegistrationState.Loading -> {
                // 显示加载指示器
            }
            is RegistrationState.Success -> {
                // 注册成功，显示成功消息并连接MQ
                Toaster.show("注册成功！")
                // 注册成功后自动连接MQ
                connectMQAfterRegistration()
                finish()
            }
            is RegistrationState.Error -> {
                // 注册失败，显示错误消息
                Toaster.show("注册失败：${state.error}")
            }
        }
    }

    /**
     * 更新表单验证UI
     */
    private fun updateValidationUI(validationState: FormValidationState) {
        // 根据验证状态更新UI，比如显示错误提示
        // 可以在这里添加字段错误提示的逻辑

        // 示例：更新提交按钮状态
        // mBinding.btnSubmit.isEnabled = validationState.isValid
    }

    /**
     * 填充已有设备数据到UI组件
     * 当用户选择绑定已有无人机时，将选中设备的信息填充到对应的输入框
     */
    private fun fillExistingDeviceDataToUI(device: UnbindUavBean) {
        // 只在绑定已有无人机模式下填充数据
        if (mViewModel.droneBindingMode.value == DroneBindingMode.EXISTING_DRONE) {

            //填充无人机名称到下拉选择框
            mBinding.esUavName.setText(device.uavName)

            //填充无人机编号到输入框
            mBinding.etUavNumber.setText(device.uavId)

            //填充FCSN到输入框（如果有的话）
            device.fcsn?.let { fcsn ->
                mBinding.etUavFcSn.setText(fcsn)
            } ?: run {
                // 如果fcsn为空，使用sn作为备用
                mBinding.etUavFcSn.setText(device.sn)
            }

            //填充品牌到输入框
            mBinding.etUavFcProducer.setText(device.brand)

            //填充型号到下拉选择框
            // 需要将型号代码转换为显示名称
            val modelName = getModelNameByCode(device.model)
            if (modelName != null) {
                mBinding.esUavModel.setText(modelName)
            } else {
                // 如果找不到对应的显示名称，直接显示型号代码
                mBinding.esUavModel.setText(device.model)
            }

            //禁用这些输入框，因为已有设备的信息不应该被修改
            setExistingDeviceFieldsEditable(false)

            // 记录日志用于调试
            XLogUtil.d("RegisterStation",
                "填充已有设备数据: 名称=${device.uavName}, 编号=${device.uavId}, " +
                "型号=${device.model}, 品牌=${device.brand}, FCSN=${device.fcsn ?: device.sn}, SN=${device.sn}")
        }
    }

    /**
     * 设置已有设备相关字段的可编辑状态
     * @param editable true表示可编辑，false表示只读
     */
    private fun setExistingDeviceFieldsEditable(editable: Boolean) {
        // 序列号字段
        mBinding.etUavNumber.isEnabled = editable

        // FCSN字段
        mBinding.etUavFcSn.isEnabled = editable

        // 品牌字段
        mBinding.etUavFcProducer.isEnabled = editable

        // 型号下拉选择框
        mBinding.esUavModel.isEnabled = editable
    }

    /**
     * 清空已有设备的数据
     * 当切换到新建无人机模式时，清空之前填充的已有设备信息
     */
    private fun clearExistingDeviceData() {
        // 清空编号
        mBinding.etUavNumber.setText("")
        // 清空FCSN
        mBinding.etUavFcSn.setText("")
        // 清空品牌（恢复默认值）
        mBinding.etUavFcProducer.setText("DJI")
        // 清空型号选择
        mBinding.esUavModel.setText("")

        // 【修复】清空已有设备名称下拉选择框
        mBinding.esUavName.setText("")

        // 清空无人机名称输入框（如果当前显示的是输入框）
        if (mViewModel.droneBindingMode.value == DroneBindingMode.NEW_DRONE) {
            // 恢复为站点名称作为默认无人机名称
            val stationName = mBinding.etStationName.text.toString()
            if (stationName.isNotEmpty()) {
                mBinding.etUavName.setText(stationName)
            } else {
                mBinding.etUavName.setText("")
            }
        }

        XLogUtil.d("RegisterStation", "已清空已有设备数据，恢复为新建模式")
    }

    // ==================== 地图相关方法 ====================

    /**
     * 初始化地图
     */
    private fun initMap(savedInstanceState: Bundle?) {
        mapView = mBinding.mapSiteLoc
        mapView.onCreate(savedInstanceState)
        // 检查定位权限
        if (checkLocationPermission()) {
            setupMap()
        } else {
            requestLocationPermission()
        }
    }

    /**
     * 设置地图
     */
    private fun setupMap() {
        mapView.let { map ->
            aMap = map.map
            // 设置地图基本属性
            aMap.mapType = AMap.MAP_TYPE_NORMAL
            aMap.isMyLocationEnabled = false
            aMap.uiSettings.isMyLocationButtonEnabled = false
            aMap.uiSettings.isZoomControlsEnabled = false
            aMap.uiSettings.isCompassEnabled = false
            aMap.uiSettings.isScaleControlsEnabled = false

            // 设置地图点击监听器
            aMap.setOnMapClickListener { latLng ->
                onMapClick(latLng)
            }

            // 设置地图加载完成监听器，在地图加载完成后执行一次定位
            aMap.setOnMapLoadedListener {
                performOnceLocation()
            }

            XLogUtil.d("RegisterStation", "地图初始化完成")
        }
    }

    /**
     * 执行一次定位
     */
    private fun performOnceLocation() {
        try {
            locationClient = AMapLocationClient(appContext)
            locationClient?.setLocationListener(this)

            val locationOption = AMapLocationClientOption().apply {
                locationMode = AMapLocationClientOption.AMapLocationMode.Hight_Accuracy
                isOnceLocation = true  // 只定位一次
                isNeedAddress = true
                httpTimeOut = 2000
                locationPurpose = AMapLocationClientOption.AMapLocationPurpose.SignIn
            }

            locationClient?.setLocationOption(locationOption)
            locationClient?.startLocation()

            XLogUtil.d("RegisterStation", "地图加载完成，开始执行一次定位...")
        } catch (e: Exception) {
            XLogUtil.e("RegisterStation", "定位失败: ${e.message}")
        }
    }

    /**
     * 地图点击事件处理
     */
    private fun onMapClick(latLng: LatLng) {
        // 移除之前的选择标记
        currentLocationMarker?.remove()
        // 添加新的选择标记
        currentLocationMarker = aMap.addMarker(
            MarkerOptions()
                .position(latLng)
                .icon(BitmapDescriptorFactory.fromResource(R.mipmap.ic_register_site_location))
        )

        // 更新位置信息到UI
        updateLocationUI(latLng)

        // 更新ViewModel中的位置数据
        mViewModel.updateSiteLocation(latLng)

        XLogUtil.d("RegisterStation", "用户选择位置: 纬度=${latLng.latitude}, 经度=${latLng.longitude}")
    }

    /**
     * 定位结果回调
     */
    override fun onLocationChanged(location: AMapLocation?) {
        location?.let {
            if (it.errorCode == 0) {
                // 定位成功
                val latLng = LatLng(it.latitude, it.longitude)
                // 移除之前的定位标记
                currentLocationMarker?.remove()
                // 添加当前位置标记
                currentLocationMarker = aMap.addMarker(
                    MarkerOptions()
                        .position(latLng)
                        .icon(BitmapDescriptorFactory.fromResource(R.mipmap.ic_register_site_location))
                )
                // 移动地图视角到当前位置
                aMap.animateCamera(
                    CameraUpdateFactory.newLatLngZoom(latLng, 17f),
                    1000,
                    null
                )
                // 更新位置信息到UI
                updateLocationUI(latLng)
                // 更新ViewModel中的位置数据
                mViewModel.updateSiteLocation(latLng)

                XLogUtil.d("RegisterStation",
                    "定位成功: 纬度=${it.latitude}, 经度=${it.longitude}, 地址=${it.address}")
            } else {
                XLogUtil.e("RegisterStation", "定位失败: ${it.errorInfo}")
            }
            // 定位完成后停止定位服务（无论成功还是失败）
            locationClient?.stopLocation()
            locationClient?.onDestroy()
            locationClient = null
        }
    }

    /**
     * 更新位置信息到UI
     */
    private fun updateLocationUI(latLng: LatLng) {
        val locationText = "${String.format("%.8f", latLng.latitude)}, ${String.format("%.8f", latLng.longitude)}"
        mBinding.etLocation.setText(locationText)
    }

    // ==================== 权限相关方法 ====================

    /**
     * 检查定位权限
     */
    private fun checkLocationPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED &&
        ContextCompat.checkSelfPermission(
            this,
            Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }

    /**
     * 请求定位权限
     */
    private fun requestLocationPermission() {
        ActivityCompat.requestPermissions(
            this,
            arrayOf(
                Manifest.permission.ACCESS_FINE_LOCATION,
                Manifest.permission.ACCESS_COARSE_LOCATION
            ),
            LOCATION_PERMISSION_REQUEST_CODE
        )
    }

    /**
     * 权限请求结果回调
     */
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        when (requestCode) {
            LOCATION_PERMISSION_REQUEST_CODE -> {
                val allPermissionsGranted = grantResults.isNotEmpty() &&
                        grantResults.all { it == PackageManager.PERMISSION_GRANTED }

                if (allPermissionsGranted) {
                    // 权限授予成功，初始化地图
                    setupMap()
                    XLogUtil.d("RegisterStation", "定位权限授予成功")
                } else {
                    // 权限被拒绝
                    Toaster.show("需要定位权限才能使用地图功能")
                    XLogUtil.w("RegisterStation", "定位权限被拒绝")
                }
            }
        }
    }

    // ==================== 生命周期方法 ====================

    override fun onResume() {
        super.onResume()
        mapView.onResume()
    }

    override fun onLowMemory() {
        super.onLowMemory()
        mapView.onLowMemory()
    }

    override fun onPause() {
        super.onPause()
        mapView.onPause()
    }

    override fun onDestroy() {
        super.onDestroy()

        // 清理防抖Handler
        updateRunnable?.let { updateHandler.removeCallbacks(it) }
        updateRunnable = null

        // 清理键盘监听器
        keyboardLayoutListener?.let { listener ->
            val rootView = findViewById<View>(android.R.id.content)
            rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
            keyboardLayoutListener = null
            XLogUtil.d("RegisterStation", "键盘监听器已清理")
        }

        // 确保定位客户端被安全销毁（通常在定位完成后已经销毁）
        locationClient?.let {
            it.stopLocation()
            it.onDestroy()
            locationClient = null
        }

        // 销毁地图
        mapView.onDestroy()

        XLogUtil.d("RegisterStation", "Activity资源已清理")
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mapView.onSaveInstanceState(outState)
    }

    // ==================== DJI 自动填充相关方法 ====================

    /**
     * 处理DJI连接状态变化
     */
    private fun handleDJIConnectionState(connectionState: DJIConnectionState) {
        if (connectionState.isConnected) {
            XLogUtil.d("RegisterStation", "DJI设备已连接: ${connectionState.productType?.name}")
        } else {
            val errorMsg = connectionState.connectionError ?: "未连接DJI设备"
            XLogUtil.w("RegisterStation", "DJI连接状态: $errorMsg")
            // 只在有明确错误时显示提示，避免频繁提示
            if (connectionState.connectionError != null) {
                Toaster.show("DJI设备连接失败: $errorMsg")
            }
        }
    }

    /**
     * 处理DJI自动填充状态变化
     */
    private fun handleAutoFillState(autoFillState: AutoFillState) {
        // 处理序列号自动填充
        if (autoFillState.isSerialNumberLoading) {
            XLogUtil.d("RegisterStation", "正在获取无人机序列号...")
        } else if (autoFillState.serialNumberError != null) {
            XLogUtil.w("RegisterStation", "获取序列号失败: ${autoFillState.serialNumberError}")
        } else if (autoFillState.autoFilledFields.contains("sn")) {
            XLogUtil.d("RegisterStation", "序列号自动填充成功")
        }

        // 处理型号自动填充
        if (autoFillState.isModelLoading) {
            XLogUtil.d("RegisterStation", "正在匹配设备型号...")
        } else if (autoFillState.modelError != null) {
            XLogUtil.w("RegisterStation", "型号匹配失败: ${autoFillState.modelError}")
        } else if (autoFillState.autoFilledFields.contains("model")) {
            XLogUtil.d("RegisterStation", "设备型号自动填充成功")
        }
    }

    // ==================== 数据缓存和恢复方法 ====================

    /**
     * 缓存新建无人机数据
     */
    private fun cacheNewDroneData() {
        cachedNewDroneData = CachedDroneData(
            uavName = mBinding.etUavName.text.toString().trim(),
            uavId = mBinding.etUavNumber.text.toString().trim(),
            uavSn = mBinding.etUavFcSn.text.toString().trim(),
            brand = mBinding.etUavFcProducer.text.toString().trim(),
            model = mBinding.esUavModel.text.toString().trim()
        )

        XLogUtil.d("RegisterStation", "已缓存新建无人机数据: $cachedNewDroneData")
    }

    /**
     * 恢复新建无人机数据
     */
    private fun restoreNewDroneData() {
        cachedNewDroneData?.let { cached ->
            // 恢复UI数据
            mBinding.etUavName.setText(cached.uavName)
            mBinding.etUavNumber.setText(cached.uavId)
            mBinding.etUavFcSn.setText(cached.uavSn)
            mBinding.etUavFcProducer.setText(cached.brand)
            mBinding.esUavModel.setText(cached.model)

            // 同步更新ViewModel中的数据
            mViewModel.updateDroneData(
                uavName = cached.uavName,
                uavId = cached.uavId,
                sn = cached.uavSn,
                brand = cached.brand,
                model = getModelCodeByName(cached.model) ?: cached.model
            )

            XLogUtil.d("RegisterStation", "已恢复新建无人机数据: $cached")

        }
    }

    /**
     * 清空新建无人机的UI数据
     */
    private fun clearNewDroneUIData() {
        mBinding.etUavName.setText("")
        mBinding.etUavNumber.setText("")
        mBinding.etUavFcSn.setText("")
        mBinding.etUavFcProducer.setText("")
        mBinding.esUavModel.setText("")

        XLogUtil.d("RegisterStation", "已清空新建无人机UI数据")
    }

    /**
     * 根据型号名称获取型号代码
     */
    private fun getModelCodeByName(modelName: String): String? {
        val modelList = mViewModel.deviceModelList.value
        return modelList?.find { it.modelName == modelName }?.modelCode
    }

    /**
     * 检查是否有新建无人机数据
     * 用于判断是否应该清空UI数据
     */
    private fun hasNewDroneData(): Boolean {
        val uavName = mBinding.etUavName.text.toString().trim()
        val uavId = mBinding.etUavNumber.text.toString().trim()
        val uavSn = mBinding.etUavFcSn.text.toString().trim()
        val brand = mBinding.etUavFcProducer.text.toString().trim()
        val model = mBinding.esUavModel.text.toString().trim()

        // 如果任何一个关键字段有数据，就认为有新建无人机数据
        val hasData = uavName.isNotEmpty() || uavId.isNotEmpty() || uavSn.isNotEmpty() ||
                     brand.isNotEmpty() || model.isNotEmpty()

        XLogUtil.d("RegisterStation",
            "检查新建无人机数据: hasData=$hasData, uavName='$uavName', uavId='$uavId', uavSn='$uavSn', brand='$brand', model='$model'")

        return hasData
    }

    // ==================== 键盘处理相关方法 ====================

    /**
     * 设置键盘状态监听器
     * 监听键盘的显示和隐藏状态，自动调整界面滚动
     */
    private fun setupKeyboardListener() {
        val rootView = findViewById<View>(android.R.id.content)
        keyboardLayoutListener = ViewTreeObserver.OnGlobalLayoutListener {
            val heightDiff = rootView.rootView.height - rootView.height
            val keyboardThreshold = dpToPx(200f) // 键盘高度阈值

            if (heightDiff > keyboardThreshold) {
                // 键盘弹出
                if (!isKeyboardVisible) {
                    isKeyboardVisible = true
                    onKeyboardShown()
                    XLogUtil.d("RegisterStation", "键盘已弹出，高度差: ${heightDiff}px")
                }
            } else {
                // 键盘隐藏
                if (isKeyboardVisible) {
                    isKeyboardVisible = false
                    onKeyboardHidden()
                    XLogUtil.d("RegisterStation", "键盘已隐藏")
                }
            }
        }

        rootView.viewTreeObserver.addOnGlobalLayoutListener(keyboardLayoutListener)
        XLogUtil.d("RegisterStation", "键盘监听器已设置")
    }

    /**
     * 键盘弹出时的处理
     * 确保当前焦点的输入框可见
     */
    private fun onKeyboardShown() {
        val focusedView = currentFocus
        focusedView?.let { view ->
            // 延迟执行，确保键盘完全弹出后再滚动
            mBinding.scrollView.postDelayed({
                scrollToView(view)
            }, 100)
        }
    }

    /**
     * 键盘隐藏时的处理
     * 可以在这里添加键盘隐藏后的逻辑
     */
    private fun onKeyboardHidden() {
        // 键盘隐藏后的处理逻辑（如果需要）
        XLogUtil.d("RegisterStation", "键盘隐藏处理完成")
    }

    /**
     * 设置输入框焦点监听器
     * 为所有输入框添加焦点变化监听，确保获得焦点时自动滚动到可见区域
     */
    private fun setupInputFocusListeners() {
        val inputFields = listOf(
            mBinding.etStationName,
            mBinding.etReturnHeight,
            mBinding.etEllipsoidHeight,
            mBinding.etUavName,
            mBinding.etUavNumber,
            mBinding.etUavFcSn,
            mBinding.etUavFcProducer,
            mBinding.etRealVideoConfig,
            mBinding.etRealVideoPush,
            mBinding.etLocation
        )

        inputFields.forEach { editText ->
            editText.setOnFocusChangeListener { view, hasFocus ->
                if (hasFocus) {
                    XLogUtil.d("RegisterStation", "输入框获得焦点: ${view.javaClass.simpleName}")
                    // 延迟滚动，等待键盘完全弹出
                    mBinding.scrollView.postDelayed({
                        scrollToView(view)
                    }, 300)
                }
            }
        }

        XLogUtil.d("RegisterStation", "输入框焦点监听器已设置，共${inputFields.size}个输入框")
    }

    /**
     * 滚动到指定视图
     * 确保指定的视图在滚动容器中可见
     * @param view 需要滚动到的目标视图
     */
    private fun scrollToView(view: View) {
        val scrollBounds = Rect()
        mBinding.scrollView.getHitRect(scrollBounds)

        // 检查视图是否在可见区域内
        if (!view.getLocalVisibleRect(scrollBounds)) {
            // 计算滚动位置，添加一些边距确保完全可见
            val scrollY = view.top - dpToPx(80f) // 80dp的上边距
            mBinding.scrollView.smoothScrollTo(0, scrollY.toInt())
            XLogUtil.d("RegisterStation", "滚动到视图: ${view.javaClass.simpleName}, scrollY: $scrollY")
        } else {
            XLogUtil.d("RegisterStation", "视图已在可见区域内: ${view.javaClass.simpleName}")
        }
    }

    /**
     * dp转px工具方法
     * @param dp dp值
     * @return 对应的px值
     */
    private fun dpToPx(dp: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            resources.displayMetrics
        )
    }

    // ==================== MQ连接相关方法 ====================

    /**
     * 注册成功后连接MQ
     */
    private fun connectMQAfterRegistration() {
        try {
            XLogUtil.d("RegisterStation", "=== 注册成功，开始连接MQ ===")

            val currentSN = getCurrentDeviceSN()
            if (currentSN != null && currentSN.isNotEmpty()) {
                XLogUtil.d("RegisterStation", "获取到设备SN: $currentSN，开始连接MQ")
                MQttManager.getInstance().onUserLogin(currentSN)
                XLogUtil.d("RegisterStation", "MQ连接请求已发送")
            } else {
                XLogUtil.w("RegisterStation", "无法获取设备SN，跳过MQ连接")
                // 尝试异步获取SN
                getCurrentDeviceSNAsync { sn ->
                    if (sn != null && sn.isNotEmpty()) {
                        XLogUtil.d("RegisterStation", "异步获取到设备SN: $sn，开始连接MQ")
                        MQttManager.getInstance().onUserLogin(sn)
                    } else {
                        XLogUtil.w("RegisterStation", "异步获取设备SN也失败，无法连接MQ")
                    }
                }
            }
        } catch (e: Exception) {
            XLogUtil.e("RegisterStation", "注册成功后连接MQ失败: ${e.message}")
        }
    }

    /**
     * 获取当前设备序列号（同步方式）
     * 优先从缓存获取，缓存无效时从DJI SDK获取
     */
    private fun getCurrentDeviceSN(): String? {
        return try {
            // 1. 优先从缓存获取
            val cachedSN = StorageExtKt.getMmkv().getString(ValueKey.UAV_SN, null)
            if (cachedSN != null && cachedSN.isNotEmpty()) {
                XLogUtil.d("RegisterStation", "从缓存获取到SN: $cachedSN")
                return cachedSN
            }

            // 2. 从DJI SDK同步获取（可能返回null）
            val sn = KeyManager.getInstance().getValue(
                KeyTools.createKey(FlightControllerKey.KeySerialNumber)
            )

            if (sn != null && sn.isNotEmpty()) {
                XLogUtil.d("RegisterStation", "从DJI SDK获取到SN: $sn")
                // 更新缓存
                StorageExtKt.getMmkv().putString(ValueKey.UAV_SN, sn)
                sn
            } else {
                XLogUtil.w("RegisterStation", "DJI SDK返回的SN为空")
                null
            }
        } catch (e: Exception) {
            XLogUtil.e("RegisterStation", "获取设备SN失败: ${e.message}")
            null
        }
    }

    /**
     * 异步获取设备序列号
     */
    private fun getCurrentDeviceSNAsync(callback: (String?) -> Unit) {
        try {
            KeyManager.getInstance().getValue(
                KeyTools.createKey(FlightControllerKey.KeySerialNumber),
                object : CommonCallbacks.CompletionCallbackWithParam<String> {
                    override fun onSuccess(sn: String?) {
                        if (sn != null && sn.isNotEmpty()) {
                            XLogUtil.d("RegisterStation", "异步获取设备SN成功: $sn")
                            // 更新缓存
                            StorageExtKt.getMmkv().putString(ValueKey.UAV_SN, sn)
                            callback(sn)
                        } else {
                            XLogUtil.w("RegisterStation", "异步获取的SN为空")
                            callback(null)
                        }
                    }

                    override fun onFailure(error: dji.v5.common.error.IDJIError) {
                        XLogUtil.e("RegisterStation", "异步获取设备SN失败: ${error.description()}")
                        callback(null)
                    }
                }
            )
        } catch (e: Exception) {
            XLogUtil.e("RegisterStation", "异步获取设备SN异常: ${e.message}")
            callback(null)
        }
    }
}